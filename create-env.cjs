// create-env.cjs
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Path to .env file
const envPath = path.resolve(process.cwd(), '.env');

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.log('Creating .env file with default values...');
  
  // Default environment variables
  const defaultEnv = `
# Environment
NODE_ENV=development

# Server
PORT=5174
HOST=localhost

# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/trend_ims
MONGODB_DB_NAME=trend_ims

# Next Auth
NEXTAUTH_URL=http://localhost:5174
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# Sentry (optional)
# SENTRY_DSN=
# SENTRY_AUTH_TOKEN=
# NEXT_PUBLIC_SENTRY_DSN=

# API Keys and External Services
# Add any additional API keys or service credentials here

# Other Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:5174
`;

  // Write the .env file
  fs.writeFileSync(envPath, defaultEnv.trim());
  console.log('.env file created successfully.');
} else {
  console.log('.env file already exists.');
  
  // Parse existing .env file
  const existingEnv = dotenv.parse(fs.readFileSync(envPath));
  
  // Check for critical missing variables
  const requiredVars = ['MONGODB_URI', 'MONGODB_DB_NAME'];
  const missingVars = requiredVars.filter(varName => !existingEnv[varName]);
  
  if (missingVars.length > 0) {
    console.warn(`Warning: The following required variables are missing in your .env file: ${missingVars.join(', ')}`);
    console.warn('Your application may not function correctly without them.');
  }
}

// Load the .env file
dotenv.config();

console.log('Environment variables loaded successfully.');