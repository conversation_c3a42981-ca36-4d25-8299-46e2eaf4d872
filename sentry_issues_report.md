# Sentry Issues Report for Trend_IMS (ims-tej)

**Date:** 2025-06-06
**Project:** ims-tej (trendtech-innovations)

**Note:** Sentry Seer AI analysis was attempted but is not enabled for this organization. The following analysis and plans are based on manual review and existing project knowledge (memories). It's recommended to enable Seer for future automated insights.

## Detailed Analysis of Top 3 Prioritized Issues

---

### 1. Issue: `IMS-TEJ-1Z`
   - **Description**: `GET /api/assemblies/[id] - 65f000030000000000000001`
   - **Culprit**: `GET /api/assemblies/65f000030000000000000001`
   - **Occurrences**: 1749
   - **First Seen**: 2025-05-18T13:43:25.868Z
   - **Last Seen**: 2025-06-06T07:08:49.000Z
   - **URL**: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1Z](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1Z)

   **Potential Causes & Plan:**
   - This error indicates a problem when fetching a specific assembly with ID `65f000030000000000000001`.
   - **Memory Relevance**:
     - `MEMORY[45269d85-9e23-4b4b-97c3-838abe5bb41b]`: Mentions that deleting core entities (Parts, Suppliers) doesn't check for dependencies in related collections like Assemblies. This could lead to assemblies referencing non-existent or corrupted parts/suppliers. The GET request might be failing due to such inconsistent data.
     - `MEMORY[4920fab8-442f-4324-b0cb-c875f641f71c]`: Highlights inconsistent ID handling for parts (`_id` vs `partNumber`). If assemblies reference parts using an identifier that's not consistently resolved, it could lead to errors.
   - **Plan**:
     1. Verify if the assembly ID `65f000030000000000000001` is a valid, existing assembly.
     2. Investigate the data integrity of this specific assembly and its referenced components (parts, sub-assemblies). Check for missing or invalid references.
     3. Review the `GET /api/assemblies/[id]` route handler in `app/api/assemblies/[id]/route.ts` for:
        - Proper error handling when an assembly or its components are not found.
        - Consistent use of identifiers (MongoDB `_id` vs business keys) when populating related data.
     4. Implement stricter checks during deletion of parts/suppliers to prevent orphaning assembly records, as suggested in `MEMORY[45269d85-9e23-4b4b-97c3-838abe5bb41b]`.
    - **Resolution (2025-06-06)**: Enhanced error handling in `app/services/assembly.service.ts`. Specifically, improved `handleMongoDBError` to better identify `CastError` (e.g., from invalid `partId` in `partsRequired` during population), add specific Sentry tags, and return a 400 HTTP status. Modified `getAssemblyById` and `getAssemblyByAssemblyCode` to propagate this status code. This should prevent generic 500 errors for such data inconsistencies and provide clearer diagnostics.

---

### 2. Issue: `IMS-TEJ-2Y`
   - **Description**: `POST /api/inventory/batch-stock`
   - **Culprit**: `POST /api/inventory/batch-stock`
   - **Occurrences**: 541
   - **First Seen**: 2025-05-30T14:54:36.908Z
   - **Last Seen**: 2025-05-30T16:24:58.000Z
   - **URL**: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2Y](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2Y)

   **Potential Causes & Plan:**
   - This error occurs when trying to update batch stock levels.
   - **Memory Relevance**:
     - `MEMORY[dea1fc8a-c4e7-4237-a4fb-383de8dbef40]`: Points to the coexistence of legacy `transaction.model.ts` and new `inventorytransaction.model.ts` schemas, with complex and brittle logic in `app/api/inventory-transactions/route.ts`. The `batch-stock` endpoint is highly likely to be involved with inventory transactions and could be affected by this dual-schema complexity.
   - **Plan**:
     1. Examine the `POST /api/inventory/batch-stock` endpoint (likely within `app/api/inventory-transactions/route.ts`).
    2. Check for data validation errors, schema mismatches, or issues in the logic that handles the two different transaction models.
    3. Prioritize completing the migration to the new `inventorytransaction.model.ts` and removing the legacy `transaction.model.ts` and associated handling logic. This will simplify the codebase and reduce the likelihood of such errors, as suggested in `MEMORY[dea1fc8a-c4e7-4237-a4fb-383de8dbef40]`.
    4. Ensure robust error handling and logging within this endpoint to capture more specific details about the failure.
    - **Resolution (2025-06-07)**: Enhanced the `POST` handler in `app/api/inventory-transactions/route.ts`. Added detailed logging for request bodies and improved logging for validation, 'not found', and 'insufficient stock' errors. Crucially, implemented a check to explicitly reject array payloads (potential batch attempts) with a 400 error, as the endpoint is designed for single transactions. This clarifies the endpoint's current capability and will provide better diagnostics for `IMS-TEJ-2Y`, helping determine if the issue stems from client misuse (e.g., sending batch requests) or data-specific problems within single transactions, possibly related to schema inconsistencies noted in `MEMORY[dea1fc8a-c4e7-4237-a4fb-383de8dbef40]`. A full batch implementation or further schema migration would be a separate, larger task.

---

### 3. Issue: `IMS-TEJ-Y` (and related N+1 issues like `IMS-TEJ-1C`, `IMS-TEJ-1B`, `IMS-TEJ-1D`, `IMS-TEJ-6`)
   - **Description**: `N+1 Query`
   - **Culprit**: `GET /api/parts`
   - **Occurrences**: 416 (IMS-TEJ-Y only; others also have significant counts)
   - **First Seen**: 2025-05-13T20:28:05.363Z
   - **Last Seen**: 2025-05-18T10:37:41.000Z
   - **URL**: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-Y](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-Y)

   **Potential Causes & Plan:**
   - This is a performance issue where fetching a list of parts (`GET /api/parts`) results in numerous additional database queries (one query for the list, then N queries for related data for each of the N parts).
   - **Memory Relevance**:
     - `MEMORY[4920fab8-442f-4324-b0cb-c875f641f71c]`: While this memory discusses inconsistent ID handling in `getPart` (single part retrieval), the `GET /api/parts` endpoint (list retrieval) is in the same domain. The N+1 problem often arises from inefficiently fetching related data (e.g., supplier details, latest stock, category information) for each part in separate queries.
   - **Plan**:
     1. Review the Mongoose queries in the `GET /api/parts` route handler (likely in `app/api/parts/route.ts`).
     2. Identify which related fields or documents are being fetched for each part that might be causing the N+1 queries (e.g., using multiple `populate()` calls in a loop or inefficiently structured lookups).
     3. Optimize data fetching by:
        - Using Mongoose's `populate()` method strategically to eager-load necessary related data in a single or fewer queries. Select only the required fields from populated documents.
        - If complex aggregations are needed, consider using the MongoDB aggregation framework.
        - For very complex scenarios, investigate if a GraphQL-like approach or batching data loader patterns could be beneficial.
      4. Test the performance of the endpoint after optimizations to ensure the N+1 issue is resolved.
   - **Progress & Initial Actions (2025-06-07)**:
     - Reviewed `GET /api/parts` handler in `app/api/parts/route.ts`, which calls `fetchParts` from `app/services/mongodb.ts`.
     - Examined `fetchParts` and `getPart` functions in `app/services/mongodb.ts`. These functions attempt to populate `subParts` if `includeSubParts` option is true.
     - Reviewed `app/models/part.model.ts` and found that the `PartSchema` and `IPart` interface do **not** currently define a `subParts` field.
     - **Action Taken**: To align with the current schema and as a diagnostic step for the N+1 issue, the `populate('subParts')` calls (and related logic for selecting `subParts` fields) in `fetchParts` and `getPart` within `app/services/mongodb.ts` have been temporarily commented out.
     - **Next Steps for IMS-TEJ-Y**:
       - Monitor Sentry and application performance to see if this change impacts the N+1 query issue reported for `GET /api/parts`.
       - Clarify if `subParts` functionality is required for `Part` entities.
       - If `subParts` are necessary, the `Part` schema (`part.model.ts`) must be updated to correctly define this field and its relationship (e.g., `subParts: [{ partId: { type: Schema.Types.ObjectId, ref: 'Part' }, quantity: Number }]`). The population logic can then be reinstated and verified.
       - If `subParts` are not required, the commented-out code can be permanently removed.
       - Continue investigating other potential sources of N+1 if this change doesn't resolve it (e.g., other `populate` paths or application logic).
       - **Status (2025-06-07)**: Investigation paused. Issue remains unresolved. Key findings include a schema mismatch for `subParts` (temporarily addressed by commenting out related population logic). Further monitoring and clarification on `subParts` requirements are needed when resuming.

---

## Summary of Other Unresolved Issues

The following is a list of other unresolved issues reported by Sentry. Due to the volume, detailed analysis for each is pending. Prioritization should be based on frequency, user impact, and business criticality. (Note: The list from Sentry was truncated, so this may not be exhaustive.)

- **IMS-TEJ-2Y**: `POST /api/inventory/batch-stock` (Already detailed above)
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2Y](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2Y)
- **IMS-TEJ-16**: `GET /api/products`
  - Culprit: `GET /api/products`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-16](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-16)
  - **Analysis (2025-06-07)**:
    - The Sentry event for `IMS-TEJ-16` does not contain a specific error message or stack trace, only a generic "GET /api/products" message.
    - The `GET /api/products` route handler (`app/api/products/route.ts`) calls either `getAllProducts` or `searchProducts` from `app/services/product.service.ts`.
    - Review of `product.service.ts` and `product.model.ts` revealed that the `searchProducts` function was attempting to perform regex searches on fields that do not exist in the `Product` schema:
      - `product_id` (the schema uses `productCode` for the string business key and `_id` for the ObjectId).
      - `sku` (not defined in the schema).
      - `barcode` (not defined in the schema).
      - `tags` (an index exists, but the field is not defined in the schema).
    - Querying non-existent fields is incorrect and could lead to unexpected behavior or contribute to generic errors reported by Sentry.
  - **Action Taken (2025-06-07)**:
    - Modified `app/services/product.service.ts` in the `searchProducts` function:
      - Corrected regex search on `product_id` to use `productCode`.
      - Temporarily commented out regex searches for `sku`, `barcode`, and `tags` as these fields are not in the current `Product` schema. Added a `// TODO:` to clarify requirements for these fields.
  - **Next Steps for IMS-TEJ-16**:
    - Monitor Sentry for `IMS-TEJ-16` to see if these changes reduce or eliminate the occurrences.
    - If the issue persists, further investigation will be needed. This might include:
      - Enabling more detailed logging or Mongoose debug mode to trace queries.
      - Reviewing performance of the aggregation queries, especially if timeouts are suspected.
      - Clarifying requirements for `sku`, `barcode`, and `tags` fields. If needed, add them to the `Product` schema with appropriate indexing and re-enable search for them.
      - Consider implementing a text index for more efficient multi-field searching if performance is a concern.
- **IMS-TEJ-1C**: `N+1 Query`
  - Culprit: `GET /api/parts` (Related to IMS-TEJ-Y)
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1C](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1C)
- **IMS-TEJ-1B**: `N+1 Query`
  - Culprit: `GET /api/parts` (Related to IMS-TEJ-Y)
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1B](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1B)
- **IMS-TEJ-X**: `StrictPopulateError: Cannot populate path items.part_id because it is not in your schema. Set the strictPopulate option to false to override.`
  - Culprit: `GET /api/purchase-orders`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-X](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-X)
  - **Analysis (2025-06-07)**:
    - The error `StrictPopulateError: Cannot populate path items.part_id` clearly indicates an incorrect path used in a Mongoose `populate()` call.
    - The `GET /api/purchase-orders` route handlers call service functions `fetchPurchaseOrders` (for lists) and `getPurchaseOrder` (for single POs) in `app/services/mongodb.ts`.
    - Review of `app/models/purchaseOrder.model.ts` confirmed the schema for purchase order items uses `item_id` to reference a Part (or Assembly/Product), i.e., the correct path is `items.item_id`.
    - The `fetchPurchaseOrders` service function correctly uses `.populate({ path: 'items.item_id', model: 'Part', ... })`.
    - However, the `getPurchaseOrder` service function (used when fetching a single PO, potentially by its `_id`) was using:
      - `.populate('items.part_id')` when finding by `_id`.
      - `.populate('items.partId')` when finding by `poNumber` string.
    - Both of these paths in `getPurchaseOrder` were incorrect and inconsistent with the schema and the `fetchPurchaseOrders` function.
  - **Action Taken (2025-06-07)**:
    - Modified `app/services/mongodb.ts` in the `getPurchaseOrder` function.
    - Changed the incorrect `populate('items.part_id')` and `populate('items.partId')` calls to the correct path: `.populate({ path: 'items.item_id', model: 'Part', select: 'partNumber name description unitOfMeasure' })`.
    - This aligns the population logic with the `purchaseOrder.model.ts` schema and makes it consistent with `fetchPurchaseOrders`.
  - **Next Steps for IMS-TEJ-X**:
    - Monitor Sentry for `IMS-TEJ-X` to confirm that this change resolves the `StrictPopulateError`.
    - Consider reviewing other `populate` calls related to `PurchaseOrder` items if `item_type` can be 'Assembly' or 'Product' to ensure dynamic model population (e.g., using `refPath` in schema or dynamic model in populate options) if that level of detail is required. For now, it populates as 'Part'.
- **IMS-TEJ-1D**: `N+1 Query`
  - Culprit: `GET /api/parts` (Related to IMS-TEJ-Y)
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1D](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-1D)
- **IMS-TEJ-2C**: `Error: Part with number 'PART-XYZ-987' not found.`
  - Culprit: `POST /api/assemblies`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2C](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-2C)
  - **Analysis**: The `createAssembly` service in `app/services/assembly.service.ts` was strictly validating `partsRequired.partId` as a MongoDB ObjectId and using `Part.findById()` for lookup. This failed when a business key (`partNumber`) was provided, leading to "not found" errors if the part existed under that number but not as an ObjectId, or if the part number itself was not a valid ObjectId format.
  - **Action Taken (2024-07-26)**: Modified `createAssembly` (in `app/services/assembly.service.ts`) to use the flexible `getPart(identifier: string)` service from `app/services/mongodb.ts`. This service can resolve parts by either `_id` or `partNumber`. The `createAssembly` function now correctly retrieves the part's actual `_id` for storage in the assembly document.
  - **Status**: Resolved. Monitor Sentry for recurrence. This fix also addresses IMS-TEJ-27 and IMS-TEJ-26.
  - **Further Action (2025-06-07)**: Similar refactoring to use the flexible `getPart` service was applied to `updateAssembly` and `updateAssemblyByAssemblyCode` in `app/services/assembly.service.ts`. This ensures that `PUT /api/assemblies/[id]` and `PUT /api/assemblies/code/[assemblyCode]` also correctly handle part identifiers (ObjectId or partNumber) within `partsRequired` during assembly updates, preventing similar errors.
- **IMS-TEJ-27**: `Error: Invalid part ID format for part: DL23.108`
  - Culprit: `POST /api/assemblies`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-27](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-27)
  - **Analysis**: The `createAssembly` service in `app/services/assembly.service.ts` was strictly validating `partsRequired.partId` as a MongoDB ObjectId. When a `partNumber` (which is not a valid ObjectId format) was provided, this led to "Invalid part ID format" errors.
  - **Action Taken (2024-07-26)**: Modified `createAssembly` (in `app/services/assembly.service.ts`) to use the flexible `getPart(identifier: string)` service from `app/services/mongodb.ts`. This service attempts lookup by `_id` if the identifier is a valid ObjectId, and falls back to `partNumber` lookup otherwise. This handles cases where `partId` is a `partNumber`.
  - **Status**: Resolved. Monitor Sentry for recurrence. This fix also likely addresses IMS-TEJ-26.
- **IMS-TEJ-26**: `Error: Invalid part ID format for part: DL23.108 (Similar to IMS-TEJ-27)
  - **Note**: Likely resolved by the same fix applied to IMS-TEJ-2C and IMS-TEJ-27 (flexible part lookup in `createAssembly` service using `getPart` from `mongodb.ts`). Monitor Sentry.
  - Culprit: `POST /api/assemblies`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-26](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-26)
- **IMS-TEJ-25**: `DELETE /api/assemblies/[id] - 681f796bd6a21248b8ec7640`
  - Culprit: `DELETE /api/assemblies/681f796bd6a21248b8ec7640`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-25](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-25)
  - **Note**: This could be failing due to dependency issues, as highlighted in `MEMORY[45269d85-9e23-4b4b-97c3-838abe5bb41b]`. The deletion should check if the assembly is used elsewhere or if it has components that need special handling.
- **IMS-TEJ-19**: `CastError: Cast to ObjectId failed for value "/sp/i" (type RegExp) at path "_id" for model "Part"`
  - Culprit: `GET /api/parts/search`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-19](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-19)
  - **Note**: This indicates that the search functionality is attempting to use a regular expression or a non-ObjectId string as an `_id` for searching. The search logic needs to differentiate between searching by `_id` (which requires a valid ObjectId string) and searching by other fields like `partNumber` or `name` (which can use regex or string matching). This is directly related to `MEMORY[4920fab8-442f-4324-b0cb-c875f641f71c]`.
  - **Update (2025-06-07)**: Regarding `MEMORY[4920fab8-442f-4324-b0cb-c875f641f71c]` and the `GET /api/parts/[id]` route: Review confirmed this route already utilizes the `getPart` service, which supports lookups by both `_id` and `partNumber`. No further action on this specific route for ID handling is needed.
- **IMS-TEJ-18**: `PUT /api/assemblies/65f000030000000000000001`
  - Culprit: `PUT /api/assemblies/65f000030000000000000001`
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-18](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-18)
  - **Note**: Error updating the same assembly ID as in `IMS-TEJ-1Z`. Likely related causes.
- **IMS-TEJ-6**: `N+1 Query`
  - Culprit: `GET /api/parts` (Related to IMS-TEJ-Y)
  - URL: [https://trendtech-innovations.sentry.io/issues/IMS-TEJ-6](https://trendtech-innovations.sentry.io/issues/IMS-TEJ-6)

---

## General Recommendations & Next Steps

1.  **Enable Sentry Seer AI**: For automated root cause analysis and fix suggestions in the future.
2.  **Address N+1 Queries**: Proactively review endpoints (especially list endpoints like `/api/parts`, `/api/products`, `/api/assemblies`) for N+1 query patterns and optimize them.
3.  **Schema Migration (`inventorytransaction.model.ts`)**: Prioritize completing the migration from the legacy `transaction.model.ts` to `inventorytransaction.model.ts` to simplify logic and reduce errors in inventory-related operations, as per `MEMORY[dea1fc8a-c4e7-4237-a4fb-383de8dbef40]`.
4.  **Consistent ID Handling**: Standardize how resource identifiers (MongoDB `_id` vs. business keys like `partNumber`) are handled across API routes and services, particularly for parts and assemblies, as highlighted in `MEMORY[4920fab8-442f-4324-b0cb-c875f641f71c]`. Ensure validation and clear differentiation in lookup logic.
5.  **Dependency Checks on Deletion**: Implement robust dependency checks before deleting core entities (Parts, Suppliers, Assemblies) to maintain data integrity, as per `MEMORY[45269d85-9e23-4b4b-97c3-838abe5bb41b]`. Return appropriate error codes (e.g., 409 Conflict) if dependencies exist.
6.  **Configuration Consolidation**: Address the redundant environment variables for URLs as noted in `MEMORY[a4f22ad0-eabc-4a18-9db1-00ca964a483c]` to prevent configuration errors, although not directly a Sentry issue, it's good practice for stability.
7.  **Detailed Investigation**: For each Sentry issue, use the Sentry UI to examine stack traces, tags, and context to pinpoint the exact line of code and conditions causing the error.
8.  **Inventory Transaction Consistency (2025-06-07)**: The `POST /api/inventory-transactions` route handler in `app/api/inventory-transactions/route.ts` was updated. Specifically, the `Inventory.findOneAndUpdate` queries now correctly use the resolved `part._id` (ObjectId) instead of `data.partId` (which could be a `partNumber`). This ensures consistency and correctness when updating inventory records.

This report should provide a good starting point for tackling these issues.