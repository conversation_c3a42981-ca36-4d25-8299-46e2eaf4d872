import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new supplier service
import { 
  getSupplierBySupplierId, 
  updateSupplierBySupplierId, 
  deleteSupplierBySupplierId, 
  handleMongoDBError 
} from '@/app/services/supplier.service';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the supplier_id field
}

/**
 * GET handler for fetching a specific supplier by its supplier_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the supplier ID
 * @returns JSON response with supplier data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const supplierId = params.id; // The unique string identifier for the supplier
  try {
    console.log(`[API] GET /api/suppliers/${supplierId} - Fetching supplier`);

    // Call the service function to get the supplier
    const supplier = await getSupplierBySupplierId(supplierId);

    const duration = Date.now() - startTime;

    if (!supplier) {
      console.log(`[API] Supplier ${supplierId} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Supplier with ID ${supplierId} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched supplier ${supplierId} successfully (${duration}ms)`);
    return NextResponse.json({ data: supplier, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching supplier ${supplierId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific supplier by its supplier_id
 * @param request - The incoming request with updated supplier data
 * @param params - Route parameters including the supplier ID
 * @returns JSON response with updated supplier data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const supplierId = params.id;
  try {
    console.log(`[API] PUT /api/suppliers/${supplierId} - Updating supplier`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Call the service function to update the supplier
    const updatedSupplier = await updateSupplierBySupplierId(supplierId, updateData);

    const duration = Date.now() - startTime;

    if (!updatedSupplier) {
      console.log(`[API] Supplier ${supplierId} not found for update (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Supplier with ID ${supplierId} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Updated supplier ${supplierId} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedSupplier, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating supplier ${supplierId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific supplier by its supplier_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the supplier ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const supplierId = params.id;
  try {
    console.log(`[API] DELETE /api/suppliers/${supplierId} - Deleting supplier`);

    // Call the service function to delete the supplier
    const serviceResult = await deleteSupplierBySupplierId(supplierId);
    const duration = Date.now() - startTime;

    if (!serviceResult.success) {
      console.log(`[API] DELETE /api/suppliers/${supplierId} - Failed (${duration}ms): ${serviceResult.message}`);
      return NextResponse.json(
        { success: false, error: serviceResult.message, meta: { duration } },
        { status: serviceResult.statusCode }
      );
    }

    console.log(`[API] DELETE /api/suppliers/${supplierId} - Succeeded (${duration}ms): ${serviceResult.message}`);
    return NextResponse.json({
      success: true,
      message: serviceResult.message,
      data: serviceResult.data,
      error: null, // Explicitly null on success
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting supplier ${supplierId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: message, meta: { duration } },
      { status }
    );
  }
}
