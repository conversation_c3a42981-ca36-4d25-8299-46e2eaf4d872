import { NextRequest, NextResponse } from 'next/server';
import { getProductsByCategory } from '@/app/services/product.service';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import mongoose from 'mongoose';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;
const ROUTE_PATH = '/api/products/category';

/**
 * GET handler for fetching products by category
 * @param request - The incoming request
 * @returns JSON response with products data
 */
async function handleGET(request: NextRequest) {
    const startTime = Date.now(); // Re-add startTime for service call duration
    console.log('[API] GET /api/products/category - Fetching products by category');
    const url = new URL(request.url);

    // Get category ID (required)
    const categoryId = url.searchParams.get('category_id');
    if (!categoryId) {
      throw new InvalidParameterError('Category ID is required');
    }

    // Validate category ID format
    if (!mongoose.Types.ObjectId.isValid(categoryId)) {
      throw new InvalidParameterError('Invalid category ID format');
    }

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Additional filters
    const filter: any = {};
    
    // Status filter
    const status = url.searchParams.get('status');
    if (status) {
      filter.status = status;
    }
    
    // Price range filter
    const minPrice = url.searchParams.get('minPrice');
    const maxPrice = url.searchParams.get('maxPrice');
    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = parseFloat(minPrice);
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice);
    }

    // Prepare options for service function
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling getProductsByCategory service with categoryId: ${categoryId}, options: ${JSON.stringify(options)}`);

    // Call service function
    const serviceStartTime = Date.now();
    const result = await getProductsByCategory(categoryId, options);
    const serviceDuration = Date.now() - serviceStartTime;

    console.log(`[API] Service getProductsByCategory completed in ${serviceDuration}ms with ${result.products.length} results`);

    // Return response
    return successResponse(
      result.products,
      'Products by category retrieved successfully',
      { pagination: result.pagination, categoryId, duration: serviceDuration }
    );
}

export const GET = withErrorHandling(handleGET, ROUTE_PATH);