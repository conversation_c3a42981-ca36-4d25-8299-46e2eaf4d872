import { NextRequest, NextResponse } from 'next/server';
import { getAllProducts, createProduct, searchProducts } from '@/app/services/product.service';
import { successResponse, validationErrorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

// Maximum allowed limit per request
const MAX_LIMIT = 500;
const ROUTE_PATH = '/api/products';

/**
 * GET handler for fetching products with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with products data
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);
  
  console.log('[API] GET /api/products - Fetching products');
  const url = new URL(request.url);

  // --- Parsing Query Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

  const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
  const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

  // --- Building Filter Object (Aligned with New Schema) ---
  const filter: any = {};
  
  // Filter by field
  const status = url.searchParams.get('status');
  const categoryId = url.searchParams.get('category_id');
  const productId = url.searchParams.get('product_id');
  const name = url.searchParams.get('name');
  const sku = url.searchParams.get('sku');
  const barcode = url.searchParams.get('barcode');
  const query = url.searchParams.get('query');

  if (status) filter.status = status;
  if (categoryId) filter.category_id = categoryId; 
  if (productId) filter.product_id = productId;
  if (name) filter.name = new RegExp(name, 'i');
  if (sku) filter.sku = sku;
  if (barcode) filter.barcode = barcode;
  
  // If query parameter is provided, use the search function instead
  if (query) {
    console.log(`[API] Searching products with query: ${query}`);
    
    const options = {
      query,
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter
    };
    
    const result = await searchProducts(options);
    
    const duration = Date.now() - startTime;
    console.log(`[API] Service searchProducts completed in ${duration}ms`);
    
    return successResponse(
      result?.products,
      'Products retrieved successfully',
      { 
        duration,
        pagination: result?.pagination
      }
    );
  }

  // --- Prepare Options for Service Function ---
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter,
  };

  console.log(`[API] Calling getAllProducts service with options: ${JSON.stringify(options)}`);

  // --- Call Service Function ---
  const result = await getAllProducts(options); 

  const duration = Date.now() - startTime;
  console.log(`[API] Service getAllProducts completed in ${duration}ms`);

  // --- Return Response ---
  return successResponse(
    result?.products,
    'Products retrieved successfully',
    { 
      duration,
      pagination: result?.pagination
    }
  );
}

/**
 * POST handler for creating a new product
 * 
 * @param request - The incoming request with product data
 * @returns JSON response with the newly created product
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('POST', ROUTE_PATH, null, true);
  
  console.log('[API] POST /api/products - Creating new product');
  const productData = await request.json();

  // Basic validation
  if (!productData || typeof productData !== 'object') {
    throw new InvalidParameterError('Invalid product data provided');
  }
  
  // Validate required fields based on the new schema
  const requiredFields = ['product_id', 'name', 'price'];
  const missingFields = requiredFields.filter(field => !(field in productData));
  if (missingFields.length > 0) {
    throw new InvalidParameterError(`Missing required fields: ${missingFields.join(', ')}`, 
      missingFields.map(field => ({ field, message: 'This field is required' }))
    );
  }

  console.log(`[API] Calling createProduct service with data: ${JSON.stringify(productData)}`);

  // Call the createProduct service function
  const savedProduct = await createProduct(productData);

  const duration = Date.now() - startTime;
  console.log(`[API] Service createProduct completed in ${duration}ms`);

  // Return the saved product (service function handles creation logic)
  return successResponse(
    savedProduct,
    'Product created successfully',
    { duration },
    201
  );
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const POST = withErrorHandling(handlePOST, ROUTE_PATH);
