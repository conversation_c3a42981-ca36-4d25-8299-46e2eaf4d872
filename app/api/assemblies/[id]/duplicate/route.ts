import { NextRequest, NextResponse } from 'next/server';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
// Use functions from assembly.service.ts for schema-aware operations
import { 
  getAssemblyByAssemblyCode, 
  createAssembly
  // handleMongoDBError as handleAssemblyServiceError // No longer needed here
} from '@/app/services/assembly.service'; 
// Attempting the logically correct relative path for interfaces again
import { IAssemblyPart, IImage, IAttribute, IAssembly } from '@/app/models/assembly.model'; 
import { CreateAssemblyDto } from '@/app/services/assembly.service'; // Import DTO

const ROUTE_PATH = '/api/assemblies/[id]/duplicate';

interface RouteParams {
  id: string; // This is assemblyCode
}

/**
 * POST handler for duplicating an assembly
 * @param _request - The incoming request (unused)
 * @param context - Context object containing route parameters
 * @param context.params - Route parameters containing the assembly ID (assemblyCode)
 * @returns JSON response with the duplicated assembly or error
 */
async function handlePOSTDuplicate(
  _request: NextRequest,
  context?: { params: RouteParams } // Make context optional to align with middleware
) {
  const startTime = Date.now();
  if (!context || !context.params) {
    // This should not be reached in a dynamic route if Next.js provides params as expected.
    console.error(`[API] Critical: context or context.params missing in dynamic route handler for ${ROUTE_PATH}`);
    throw new Error('Internal Server Error: Route parameters missing.');
  }
  const { params } = context; // params is now RouteParams
  const assemblyCodeToDuplicate = params.id; // id is the assemblyCode from the route
  try {
    console.log(`[API] POST /api/assemblies/${assemblyCodeToDuplicate}/duplicate - Duplicating assembly`);

    const originalAssembly = await getAssemblyByAssemblyCode(assemblyCodeToDuplicate, true);

    if (!originalAssembly) {
      console.log(`[API] Assembly ${assemblyCodeToDuplicate} not found for duplication`);
      return NextResponse.json(
        { success: false, error: `Assembly with code ${assemblyCodeToDuplicate} not found`, meta: { duration: Date.now() - startTime } },
        { status: 404 }
      );
    }

    // Map originalAssembly to CreateAssemblyDto
    const newAssemblyDto: CreateAssemblyDto = {
      assemblyCode: `${originalAssembly.assemblyCode}-COPY-${Date.now().toString().slice(-5)}`,
      name: `${originalAssembly.name} (Copy)`,
      description: originalAssembly.description || undefined,
      productId: originalAssembly.productId?.toString() || undefined, // Ensure string or undefined
      parentId: originalAssembly.parentId?.toString() || undefined,   // Ensure string or undefined
      isTopLevel: originalAssembly.isTopLevel,
      partsRequired: originalAssembly.partsRequired?.map((p: IAssemblyPart) => {
        // If p.partId is populated, it's an object like { _id: ..., name: ... }.
        // If it's just an ObjectId string/object, .toString() would work directly or on ._id if it's an ObjectId instance.
        // The DTO expects a string representation of the ObjectId.
        const partIdString = (typeof p.partId === 'string') 
          ? p.partId 
          : (p.partId && typeof (p.partId as any)._id !== 'undefined') 
            ? (p.partId as any)._id.toString() 
            : p.partId?.toString() || ''; // Fallback for ObjectId instance, ensure string
        return {
          partId: partIdString,
          quantityRequired: p.quantityRequired || 1, // Ensure quantity is at least 1
          unitOfMeasure: p.unitOfMeasure
        };
      }) || [],
      status: originalAssembly.status || 'pending_review', // Default status
      version: 1, // Reset version for a new copy
      manufacturingInstructions: originalAssembly.manufacturingInstructions || undefined,
      estimatedBuildTime: originalAssembly.estimatedBuildTime || undefined,
      notes: originalAssembly.notes || undefined,
      createdBy: null, // TODO: Get actual user ID from request context (e.g., session or middleware)
      // Legacy fields from DTO if needed for the createAssembly service function
      // assembly_id: will be handled by model pre-save or createAssembly logic if it uses assemblyCode
      images: originalAssembly.images?.map((img: IImage) => ({ url: img.url, alt_text: img.alt_text || undefined })),
      attributes: originalAssembly.attributes?.map((attr: IAttribute) => ({ name: attr.name, value: attr.value })),
    };
    
    // Remove undefined properties to ensure they don't override Mongoose defaults if any
    Object.keys(newAssemblyDto).forEach(key => (newAssemblyDto as any)[key] === undefined && delete (newAssemblyDto as any)[key]);

    const duplicatedAssembly = await createAssembly(newAssemblyDto);

    const duration = Date.now() - startTime;
    console.log(`[API] Duplicated assembly ${assemblyCodeToDuplicate} successfully (${duration}ms)`);
    
    return NextResponse.json({ 
      success: true, 
      data: duplicatedAssembly, 
      meta: { duration } 
    });

  } catch (error: any) {
    // Errors will be caught by withErrorHandling. Re-throw to allow it to process.
    // Specific early returns (like 404) are handled above.
    console.error(`[API] Error in handlePOSTDuplicate for ${assemblyCodeToDuplicate}:`, error); 
    throw error; // Re-throw for the middleware
  }
}

export const POST = withErrorHandling(handlePOSTDuplicate, ROUTE_PATH);
