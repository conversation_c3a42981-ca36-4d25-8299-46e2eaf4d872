import { NextRequest, NextResponse } from 'next/server';
import { getAllAssemblies, createAssembly, handleMongoDBError, searchAssemblies } from '@/app/services/assembly.service';
import { successResponse, errorResponse, validationErrorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import connectToDatabase from '@/app/lib/mongodb';
import Assembly from '@/app/models/assembly.model';
import { parse } from 'url';

// Maximum allowed limit per request
const MAX_LIMIT = 500;
const ROUTE_PATH = '/api/assemblies';

// Helper to safely parse integer or return default
const parseIntOrDefault = (value: string | null, defaultValue: number): number => {
  if (value === null) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * GET handler for fetching assemblies with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with assemblies data
 */
async function handleGET(req: NextRequest) {
  try {
    // await connectToDatabase(); // Service function will handle connection

    const searchParams = req.nextUrl.searchParams;

    const page = parseIntOrDefault(searchParams.get('page'), 1);
    const limit = parseIntOrDefault(searchParams.get('limit'), MAX_LIMIT);
    // const skip = (page - 1) * limit; // skip is handled by service

    const filter: any = {};
    if (searchParams.has('status')) filter.status = searchParams.get('status');
    if (searchParams.has('type')) filter.assemblyType = searchParams.get('type');
    if (searchParams.has('assemblyCode')) filter.assemblyCode = searchParams.get('assemblyCode');
    if (searchParams.has('name')) filter.name = new RegExp(searchParams.get('name')!, 'i');
    if (searchParams.has('version')) {
      const versionVal = parseIntOrDefault(searchParams.get('version'), -1);
      if (versionVal !== -1) filter.version = versionVal;
    }
    if (searchParams.has('isTopLevel')) filter.isTopLevel = searchParams.get('isTopLevel') === 'true';

    const sort: any = {};
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');
    if (sortBy && sortOrder) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort, service might have its own default
    }
    
    const populateParts = searchParams.get('includeParts') === 'true';

    console.log(`[GET /api/assemblies] Calling service with: page=${page}, limit=${limit}, filter=${JSON.stringify(filter)}, sort=${JSON.stringify(sort)}, populateParts=${populateParts}`);

    const { assemblies, pagination } = await getAllAssemblies({
      page,
      limit,
      sort,
      filter,
      populateParts
    });
    
    // The service now returns totalCount as part of pagination object
    // And totalPages, currentPage, limit are also part of it.
    return NextResponse.json({
      success: true,
      message: 'Assemblies retrieved successfully',
      data: assemblies,
      pagination: {
        total: pagination.totalCount, // map from service's pagination
        page: pagination.currentPage,
        limit: pagination.limit,
        pages: pagination.totalPages,
      },
    }, { status: 200 });

  } catch (err: any) {
    // Error handling can be simplified if withErrorHandling and service errors are robust.
    // For now, keeping specific checks, but these might become redundant.
    console.error(`[GET /api/assemblies] Failed: ${err.message}`, {
      stack: err.stack,
      url: req.url,
      method: req.method,
      ip: req.headers.get('x-forwarded-for') || undefined,
      serviceErrorCode: err.statusCode, // If service throws error with statusCode
    });

    if (err.name === 'MongoNetworkError' || err.name === 'MongooseServerSelectionError' || err.statusCode === 503) {
      return NextResponse.json({
        success: false,
        message: 'Database connection error. Please try again later.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Database error',
        code: err.code || 'DB_UNAVAILABLE',
      }, { status: err.statusCode || 503 });
    }
    
    if (err.name === 'MongoTimeoutError' || err.message.toLowerCase().includes('timeout') || err.statusCode === 408) { 
      return NextResponse.json({
        success: false,
        message: 'Request timed out while fetching assemblies.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Request timeout',
        code: err.code || 'TIMEOUT',
      }, { status: err.statusCode || 408 });
    }
    
    // Generic error, potentially from the service or unexpected
    return NextResponse.json({
      success: false,
      message: err.message || 'Failed to fetch assemblies due to an unexpected error.',
      error: process.env.NODE_ENV === 'development' ? err.message : 'Server error',
      code: err.code || 'SERVER_ERROR',
    }, { status: err.statusCode || 500 });
  }
}

/**
 * POST handler for creating a new assembly
 * 
 * @param request - The incoming request with assembly data
 * @returns JSON response with the newly created assembly
 */
import { CanonicalCreateAssemblyDto } from '@/app/services/assembly.service';
import { 
  validateCreateAssemblyDto, 
  validatePartsExist, 
  validateSubAssembliesExist,
  validateParentAssemblyExists
} from '@/app/lib/assembly-validators';

async function handlePOST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = (request as any).requestId || 'unknown-request';
  let requestBody: any;
  
  try {
    // Log the API request
    await logApiRequest('POST', ROUTE_PATH, null, true);
    console.log(`[${requestId}] [API] POST /api/assemblies - Creating new assembly`);
    
    // Parse and validate request body
    try {
      requestBody = await request.json();
    } catch (error) {
      console.error(`[${requestId}] [API] Failed to parse request body:`, error);
      return errorResponse('Invalid JSON payload', '400');
    }

    if (!requestBody) {
      console.error(`[${requestId}] [ERROR] Request body is required`);
      return errorResponse(
        'VALIDATION_ERROR',
        'Request body is required',
        [{ field: 'body', message: 'Request body is required' }],
        400
      );
    }

    // Basic validation for the request body structure
    if (typeof requestBody !== 'object' || Array.isArray(requestBody)) {
      console.error(`[${requestId}] [API] Invalid request body type:`, typeof requestBody);
      return errorResponse(
        'INVALID_REQUEST_BODY',
        'Request body must be a valid JSON object',
        [{ field: 'body', message: 'Request body must be a valid JSON object' }],
        400
      );
    }

    // Validate the DTO structure and content
    const validationResult = validateCreateAssemblyDto(requestBody);
    if (!validationResult.success) {
      console.error(`[${requestId}] [API] Validation failed:`, validationResult.errors);
      return errorResponse(
        'VALIDATION_ERROR',
        'Request validation failed',
        validationResult.errors,
        400
      );
    }

    // Additional validations that require database lookups
    const { partsRequired = [], subAssemblies = [], parentId } = requestBody;
    
    // Validate parts existence if provided
    if (requestBody.partsRequired && requestBody.partsRequired.length > 0) {
      const { success: partsExist, errors: partsErrors } = await validatePartsExist(requestBody.partsRequired);
      if (!partsExist) {
        console.error(`[${requestId}] [ERROR] Invalid parts in request`, { partsErrors });
        return errorResponse(
          'INVALID_PARTS',
          'One or more parts are invalid or do not exist',
          partsErrors,
          400
        );
      }
    }
    
    // Validate sub-assemblies existence if provided
    if (requestBody.subAssemblies && requestBody.subAssemblies.length > 0) {
      const { success: subAssembliesExist, errors: subAssembliesErrors } = await validateSubAssembliesExist(requestBody.subAssemblies);
      if (!subAssembliesExist) {
        console.error(`[${requestId}] [ERROR] Invalid sub-assemblies in request`, { subAssembliesErrors });
        return errorResponse(
          'INVALID_SUB_ASSEMBLIES',
          'One or more sub-assemblies are invalid or do not exist',
          subAssembliesErrors,
          400
        );
      }
    }
    
    // Validate parent assembly exists if provided
    if (parentId) {
      const parentExists = await validateParentAssemblyExists(parentId);
      if (!parentExists) {
        console.error(`[${requestId}] [API] Parent assembly not found: ${parentId}`);
        return errorResponse(
          'PARENT_ASSEMBLY_NOT_FOUND',
          `Parent assembly with ID '${parentId}' not found`,
          [{ field: 'parentId', message: 'Parent assembly not found' }],
          404
        );
      }
    }

    // Check for duplicate assembly code if provided
    if (requestBody.assemblyCode) {
      const existingAssembly = await Assembly.findOne({ assemblyCode: requestBody.assemblyCode });
      if (existingAssembly) {
        console.error(`[${requestId}] [ERROR] Duplicate assembly code: ${requestBody.assemblyCode}`);
        return errorResponse(
          'DUPLICATE_ASSEMBLY_CODE',
          'An assembly with this code already exists',
          [{ field: 'assemblyCode', message: 'Assembly with this code already exists' }],
          409
        );
      }
    }

    console.log(`[${requestId}] [API] Creating assembly with code: ${requestBody.assemblyCode}`);
    
    // Call the service to create the assembly
    try {
      // Create the assembly
      const createdAssembly = await createAssembly(requestBody as CanonicalCreateAssemblyDto);
      
      console.log(`[${requestId}] [API] Assembly created successfully:`, createdAssembly.assemblyCode);
      
      return successResponse(createdAssembly, 'Assembly created successfully', undefined, 201);
    } catch (error) {
      console.error(`[${requestId}] [ERROR] Error creating assembly:`, error);
      return errorResponse(
        'ASSEMBLY_CREATION_ERROR',
        'Failed to create assembly',
        [
          {
            field: 'server',
            message: 'Failed to create assembly',
            ...(process.env.NODE_ENV === 'development' && { details: error instanceof Error ? error.message : String(error) })
          }
        ],
        500
      );
    }
    
  } catch (error: unknown) {
    const errorId = `err_${Date.now()}`;
    const duration = Date.now() - startTime;
    
    console.error(`[${requestId}] [ERROR] Unhandled error in POST /api/assemblies:`, error);
    
    // For known error types with status code, return appropriate error response
    if (error && typeof error === 'object' && 'statusCode' in error) {
      const typedError = error as { statusCode: number; code?: string; message?: string; details?: any };
      return errorResponse(
        typedError.code || 'UNKNOWN_ERROR',
        typedError.message || 'An error occurred',
        typedError.details || [{ field: 'server', message: 'An error occurred' }],
        typedError.statusCode
      );
    }
    
    // For other errors, return a 500 error
    const errorMessage = error instanceof Error ? error.message : String(error);
    return errorResponse(
      'INTERNAL_SERVER_ERROR',
      'An unexpected error occurred',
      [
        { 
          field: 'server', 
          message: 'An unexpected error occurred',
          ...(process.env.NODE_ENV === 'development' && { 
            details: errorMessage,
            errorId
          })
        }
      ],
      500
    );
  }
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const POST = withErrorHandling(handlePOST, ROUTE_PATH);
