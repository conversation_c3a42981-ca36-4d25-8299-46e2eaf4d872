// File: app/api/parts/route.ts

import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { handleMongoDBError } from '@/app/services/mongodb'; // fetchParts and addPart removed
import { createPart, CreatePartDto, getAllParts } from '@/app/services/part.service';
import { z } from 'zod';
import withDatabase from '@/app/middlewares/withDatabase';

const MAX_LIMIT = 500;

// Zod schema for strict validation of CreatePartDto
const CreatePartValidationSchema = z.object({
  partNumber: z.string().trim().min(1, { message: 'Part number is required.' }),
  name: z.string().trim().min(1, { message: 'Part name is required.' }),
  description: z.string().trim().nullable().optional(),
  technicalSpecs: z.string().trim().nullable().optional(),
  isManufactured: z.boolean(),
  reorderLevel: z.number().int().min(0).nullable().optional(),
  status: z.enum(['active', 'inactive', 'obsolete']),
  inventory: z.object({
    currentStock: z.number().int().min(0, { message: 'Current stock cannot be negative.' }),
    warehouseId: z.string().trim().min(1, { message: 'Warehouse ID is required.' }), // Further ObjectId validation happens in service/model
    safetyStockLevel: z.number().int().min(0, { message: 'Safety stock level cannot be negative.' }),
    maximumStockLevel: z.number().int().min(0, { message: 'Maximum stock level cannot be negative.' }),
    averageDailyUsage: z.number().min(0, { message: 'Average daily usage cannot be negative.' }),
    abcClassification: z.string().trim().min(1, { message: 'ABC classification is required.' }),
    lastStockUpdate: z.date().nullable().optional(),
  }),
  supplierId: z.string().trim().min(1).nullable().optional(), // Further ObjectId validation happens in service/model
  unitOfMeasure: z.string().trim().min(1, { message: 'Unit of measure is required.' }),
  costPrice: z.number().min(0, { message: 'Cost price cannot be negative.' }),
  categoryId: z.string().trim().min(1).nullable().optional(), // Further ObjectId validation happens in service/model
});

async function handleGET(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] GET /api/parts - Fetching parts');
    const url = new URL(request.url);

    // Pagination
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = Math.min(
      parseInt(url.searchParams.get('limit') || '20', 10),
      MAX_LIMIT
    );

    // Sorting
    const sortField = url.searchParams.get('sortField') || 'updatedAt';
    const sortOrder = (url.searchParams.get('sortOrder') === 'asc') ? 1 : -1;

    // Filters
    const filter: any = {};
    if (url.searchParams.get('status')) {
      filter.status = url.searchParams.get('status');
    }
    if (url.searchParams.get('isManufactured') !== null) {
      filter.isManufactured =
        url.searchParams.get('isManufactured') === 'true';
    }
    if (url.searchParams.get('categoryId')) {
      const cat = url.searchParams.get('categoryId')!;
      try {
        filter.categoryId = new mongoose.Types.ObjectId(cat);
      } catch (err) {
        console.error(`Invalid categoryId: ${cat}`, err);
        filter.categoryId = cat;
      }
    }
    // Stock range
    if (
      url.searchParams.has('minStock') ||
      url.searchParams.has('maxStock')
    ) {
      filter['inventory.currentStock'] = {};
      if (url.searchParams.get('minStock')) {
        filter['inventory.currentStock'].$gte = parseInt(
          url.searchParams.get('minStock')!,
          10
        );
      }
      if (url.searchParams.get('maxStock')) {
        filter['inventory.currentStock'].$lte = parseInt(
          url.searchParams.get('maxStock')!,
          10
        );
      }
    }

    // Pack options and call service
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      // includeSubParts is not handled by getAllParts directly; if needed, requires specific logic.
      // includeInventory is implicitly true as inventory is embedded in the Part model.
    };
    console.log('[API] getAllParts options:', options);

    const result = await getAllParts(options);

    const duration = Date.now() - startTime;
    console.log(`[API] GET completed in ${duration}ms`);

    return NextResponse.json({
      data: result?.parts || [],
      pagination: result?.pagination || {},
      error: null,
      meta: { duration },
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] GET error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] POST /api/parts - Creating new part');
    const requestBody = await request.json();

    const validationResult = CreatePartValidationSchema.safeParse(requestBody);

    if (!validationResult.success) {
      const duration = Date.now() - startTime;
      console.error(`[API] POST /api/parts - Validation Error (${duration}ms):`, validationResult.error.flatten());
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid part data provided.',
          errorDetails: validationResult.error.flatten(), // Provides detailed validation errors
          meta: { duration },
        },
        { status: 400 }
      );
    }

    // Use the validated data, which conforms to CreatePartDto
    const partData = validationResult.data;

    console.log('[API] Calling createPart service with validated data:', partData);
    const savedPart = await createPart(partData as CreatePartDto); // Cast is safe here due to Zod validation

    const duration = Date.now() - startTime;
    console.log(`[API] POST /api/parts - Part created successfully in ${duration}ms`, { partId: savedPart._id });

    return NextResponse.json(
      { data: savedPart, error: null, meta: { duration } },
      { status: 201 }
    );
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] POST /api/parts - Error (${duration}ms):`, error);
    
    let errorMessage = error.message || 'An unexpected error occurred.';
    let errorStatus = error.status || 500; 

    // If it's a known error structure from handleMongoDBError or similar custom error
    if (!(error.status && error.message && typeof error.status === 'number')) { 
        const parsedError = handleMongoDBError(error); // Assumes handleMongoDBError is robust
        errorMessage = parsedError.message;
        errorStatus = parsedError.status;
    }

    return NextResponse.json(
      { data: null, error: errorMessage, meta: { duration } },
      { status: errorStatus }
    );
  }
}

export const GET  = withDatabase(handleGET);
export const POST = withDatabase(handlePOST);