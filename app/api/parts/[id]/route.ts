import { NextRequest, NextResponse } from 'next/server';
import {
  updatePartService,
  deletePartService,
  getPartById, 
  getPartByPartNumberService,
  UpdatePartDto, 
  UpdatePartInventoryDto, 
  handleMongoDBError, 
} from '@/app/services/part.service';
// import { getPart } from '@/app/services/mongodb'; // Removed
import mongoose from 'mongoose';

/**
 * GET handler for fetching a specific part by ID
 * @param request - The incoming request
 * @param params - Route parameters including the part ID
 * @returns JSON response with part data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now();
  const identifier = decodeURIComponent(params.id);
  try {
    console.log(`[API] GET /api/parts/${identifier} - Fetching part`);
    let part;
    if (mongoose.Types.ObjectId.isValid(identifier)) {
      console.log(`[API] GET /api/parts/${identifier} - Attempting to fetch part by ObjectId using getPartById service.`);
      part = await getPartById(identifier);
    } else {
      console.log(`[API] GET /api/parts/${identifier} - Attempting to fetch part by partNumber using getPartByPartNumberService.`);
      part = await getPartByPartNumberService(identifier);
    }

    const duration = Date.now() - startTime;
    if (!part) {
      console.log(`[API] GET /api/parts/${identifier} - Part not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Part with identifier '${identifier}' not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] GET /api/parts/${identifier} - Part fetched successfully (${duration}ms)`);
    return NextResponse.json({ data: part, error: null, meta: { duration } });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] GET /api/parts/${identifier} - Error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific part by ID
 * @param request - The incoming request with updated part data
 * @param params - Route parameters including the part ID
 * @returns JSON response with updated part data
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now();
  const partId = decodeURIComponent(params.id);
  try {
    console.log(`[API] PUT /api/parts/${partId} - Updating part`);
    const requestBody = await request.json();

    if (!requestBody || Object.keys(requestBody).length === 0) {
      return NextResponse.json(
        { data: null, error: 'Update data is required and cannot be empty', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(partId)) {
        return NextResponse.json(
            { data: null, error: 'Invalid Part ID format. Must be a valid ObjectId string.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }

    // Strict DTO validation
    const allowedTopLevelKeys: (keyof UpdatePartDto)[] = [
      'name', 'description', 'technicalSpecs', 'isManufactured',
      'reorderLevel', 'status', 'inventory', 'supplierId',
      'unitOfMeasure', 'costPrice', 'categoryId'
    ];
    const allowedInventoryKeys: (keyof UpdatePartInventoryDto)[] = [
      'safetyStockLevel', 'maximumStockLevel', 'averageDailyUsage', 'abcClassification'
    ];

    const validatedUpdateData: UpdatePartDto = {};

    for (const key in requestBody) {
      if (allowedTopLevelKeys.includes(key as keyof UpdatePartDto)) {
        if (key === 'inventory') {
          if (requestBody.inventory === null || (typeof requestBody.inventory === 'object' && requestBody.inventory !== null)) {
            if (requestBody.inventory === null) {
              // Explicitly setting inventory to null is not supported by UpdatePartDto for partial updates.
              // To clear inventory fields, one would omit them or set specific fields to null if allowed by UpdatePartInventoryDto.
              // However, UpdatePartInventoryDto fields are numbers/strings, not nullable in this context.
              // This path implies an attempt to nullify the entire inventory object, which we don't support this way.
              // Let's treat `inventory: null` as an empty update for inventory for now, or one could error.
              // For simplicity, we'll allow `inventory: null` to pass through as if no inventory update was intended.
            } else {
              const validatedInventoryData: UpdatePartInventoryDto = {};
              let hasInventoryData = false;
              for (const invKey in requestBody.inventory) {
                if (allowedInventoryKeys.includes(invKey as keyof UpdatePartInventoryDto)) {
                  // @ts-ignore - assigning to validatedInventoryData
                  validatedInventoryData[invKey] = requestBody.inventory[invKey];
                  hasInventoryData = true;
                } else {
                  return NextResponse.json(
                    { data: null, error: `Invalid field in inventory object: ${invKey}`, meta: { duration: Date.now() - startTime } },
                    { status: 400 }
                  );
                }
              }
              if (hasInventoryData) {
                validatedUpdateData.inventory = validatedInventoryData;
              }
            }
          } else if (requestBody.inventory !== undefined) {
            // inventory is present but not an object or null
            return NextResponse.json(
              { data: null, error: 'Invalid type for inventory field; expected object or null.', meta: { duration: Date.now() - startTime } },
              { status: 400 }
            );
          }
        } else {
          // @ts-ignore - assigning to validatedUpdateData
          validatedUpdateData[key] = requestBody[key];
        }
      } else {
        return NextResponse.json(
          { data: null, error: `Invalid field in request body: ${key}`, meta: { duration: Date.now() - startTime } },
          { status: 400 }
        );
      }
    }

    if (Object.keys(validatedUpdateData).length === 0 && Object.keys(requestBody).length > 0) {
        // This case means the requestBody had keys, but none were valid according to our DTO.
        // The earlier loop would have caught specific invalid keys. This is a fallback.
        return NextResponse.json(
            { data: null, error: 'Update data contains no valid fields.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }
    // If validatedUpdateData is empty AND requestBody was also empty, the initial check for empty requestBody handles it.

    const updateData = validatedUpdateData;

    const updatedPart = await updatePartService(partId, updateData);
    const duration = Date.now() - startTime;

    if (!updatedPart) { // Should be handled by service throwing an error, but as a fallback.
      console.log(`[API] PUT /api/parts/${partId} - Part not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Part with ID ${partId} not found`, meta: { duration } },
        { status: 404 }
      );
    }
    console.log(`[API] PUT /api/parts/${partId} - Part updated successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedPart, error: null, meta: { duration } });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] PUT /api/parts/${partId} - Error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific part by ID
 * @param request - The incoming request
 * @param params - Route parameters including the part ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  const startTime = Date.now();
  const partId = decodeURIComponent(params.id);
  try {
    console.log(`[API] DELETE /api/parts/${partId} - Deleting part`);

    if (!mongoose.Types.ObjectId.isValid(partId)) {
        return NextResponse.json(
            { success: false, error: 'Invalid Part ID format. Must be a valid ObjectId string.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }

    await deletePartService(partId); // Now returns void
    const duration = Date.now() - startTime;
    
    console.log(`[API] DELETE /api/parts/${partId} - Part deleted successfully (${duration}ms)`);
    // Return 200 OK with a success message.
    // A 204 No Content response is also an option, but a JSON response can be more informative.
    return NextResponse.json({ success: true, message: `Part with ID ${partId} deleted successfully.`, meta: { duration } }, { status: 200 });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    // Log the error message and statusCode if available
    console.error(
      `[API] DELETE /api/parts/${partId} - Error (${duration}ms): `,
      error.message,
      error.statusCode ? `Status Code: ${error.statusCode}` : ''
    );
    
    const statusCode = error.statusCode || 500; // Use statusCode from the error if present, otherwise default to 500
    const errorMessage = error.message || 'An unexpected error occurred while deleting the part.';

    return NextResponse.json(
      { success: false, error: errorMessage, meta: { duration } },
      { status: statusCode }
    );
  }
}