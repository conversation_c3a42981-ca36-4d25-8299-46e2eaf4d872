import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { Warehouse } from '@/app/models'; 
import InventoryTransaction, { IInventoryTransaction } from '@/app/models/inventorytransaction.model';
import { IPart as IPartModel } from '@/app/models/part.model';
import { adjustStockLevelByDelta, getPartById } from '../../services/part.service'; // Added getPartById, changed updateStockLevel to adjustStockLevelByDelta
import { handleMongoDBError } from '@/app/lib/mongodb';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

// Define interface for the transaction creation request
interface CreateTransactionRequest {
  // Canonical field names
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseId: string;
  transactionType?: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity?: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;
}

/**
 * GET handler for inventory transactions
 * Fetches a list of inventory transactions with pagination and filtering options
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/inventory-transactions - Fetching transactions');
    await connectToDatabase();

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10', 10), MAX_LIMIT);
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    // Filter by part ID if provided
    const itemId = url.searchParams.get('itemId');
    if (itemId) {
      try {
        filter.itemId = new mongoose.Types.ObjectId(itemId);
      } catch (err) {
        console.error('Invalid itemId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid itemId format' },
          { status: 400 }
        );
      }
    }

    const itemType = url.searchParams.get('itemType');
    if (itemType) {
      if (!['Part', 'Assembly', 'Product'].includes(itemType)) {
        return NextResponse.json(
          { success: false, error: 'Invalid itemType format' },
          { status: 400 }
        );
      }
      filter.itemType = itemType;
    }

    // Filter by warehouse ID if provided
    const warehouseId = url.searchParams.get('warehouseId');
    if (warehouseId) {
      try {
        filter.warehouseId = new mongoose.Types.ObjectId(warehouseId);
      } catch (err) {
        console.error('Invalid warehouseId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid warehouseId format' },
          { status: 400 }
        );
      }
    }

    // Filter by transaction type if provided
    const transactionType = url.searchParams.get('transactionType');
    if (transactionType) {
      filter.transactionType = transactionType;
    }

    // Filter by date range if provided
    const startDateParam = url.searchParams.get('startDate');
    const endDateParam = url.searchParams.get('endDate');

    if (startDateParam && endDateParam) {
      filter.transactionDate = {
        $gte: new Date(startDateParam),
        $lte: new Date(endDateParam)
      };
    } else if (startDateParam) {
      filter.transactionDate = { $gte: new Date(startDateParam) };
    } else if (endDateParam) {
      filter.transactionDate = { $lte: new Date(endDateParam) };
    } else {
      // If no specific date range is provided, check for period filter
      const period = url.searchParams.get('period');
      let startDate: Date | null = null;
      const now = new Date();

      switch (period) {
        case 'day':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        filter.transactionDate = { $gte: startDate };
      }
    }

    // Execute query with population
    const transactions = await InventoryTransaction.find(filter)
      .sort({ transactionDate: -1 })
      .skip(skip)
      .limit(limit)
      .populate('partId', 'partNumber name')
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username fullName')
      .lean();

    // Get total count for pagination
    const total = await InventoryTransaction.countDocuments(filter);

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transactions query completed in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      meta: { duration }
    });
  } catch (error: any) {
    console.error('Error fetching inventory transactions:', error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to fetch inventory transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for inventory transactions
 * Creates a new inventory transaction and updates inventory levels
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log('[API] POST /api/inventory-transactions - Received request');
  let session: mongoose.ClientSession | null = null;

  try {
    await connectToDatabase();
    session = await mongoose.startSession();
    session.startTransaction();

    const data = (await request.json()) as CreateTransactionRequest;
    console.log('[API] POST /api/inventory-transactions - Request body:', data);

    // Validate required fields
    if (!data.itemId || !data.itemType || !data.warehouseId || !data.userId || data.quantity === undefined || !data.transactionType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: itemId, itemType, warehouseId, userId, quantity, transactionType' },
        { status: 400 }
      );
    }
    if (!['Part', 'Assembly', 'Product'].includes(data.itemType)) {
      return NextResponse.json({ success: false, error: 'Invalid itemType specified' }, { status: 400 });
    }

    if (!mongoose.Types.ObjectId.isValid(data.itemId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid itemId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.warehouseId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid warehouseId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.userId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid userId format' }, { status: 400 });
    }

    let part: IPartModel | null = null;
    let previousStock: number | undefined;

    if (data.itemType === 'Part') {
      part = await getPartById(data.itemId);
      if (!part) {
        throw new Error(`Part not found with ID ${data.itemId}`);
      }
      if (!part.inventory || part.inventory.currentStock === undefined || part.inventory.warehouseId === undefined) {
        throw new Error(`Inventory details missing for part ${data.itemId}. Cannot process transaction.`);
      }
      if (part.inventory.warehouseId.toString() !== data.warehouseId) {
        throw new Error(`Warehouse ID mismatch for part ${data.itemId}. Expected ${part.inventory.warehouseId}, got ${data.warehouseId}.`);
      }
      previousStock = part.inventory.currentStock;
    } else {
      // For Assembly/Product, stock management might be different or not applicable here
      // For now, we'll assume previousStock needs to be fetched or defaults if not a 'Part'
      // This part needs to be expanded based on how Assembly/Product stock is handled
      console.warn(`Stock adjustment for itemType '${data.itemType}' is not fully implemented. Assuming previous stock is 0 or managed elsewhere.`);
      // As a placeholder, if we can't get previous stock, we can't reliably calculate newStock for the transaction log for non-Parts.
      // However, the transaction log requires previousStock and newStock.
      // This indicates a need for a more robust way to get current stock for Assemblies/Products if they are to be transacted here.
      // For now, let's throw an error if it's not a Part, as adjustStockLevelByDelta is part-specific.
      throw new Error(`Stock transactions for itemType '${data.itemType}' are not yet fully supported by this endpoint for stock level adjustments.`);
    }

    // previousStock is now set conditionally above if itemType is 'Part'
    const transactionQuantity = data.quantity as number;
    let quantityChangeForUpdateStockLevel: number = transactionQuantity;
    const type = data.transactionType as string;

    if (type === 'stock_out_production' || type === 'transfer_out' || type === 'sales_shipment') {
      quantityChangeForUpdateStockLevel = -Math.abs(transactionQuantity);
    } else if (type === 'stock_in_purchase' || type === 'stock_in_production' || type === 'transfer_in') {
      quantityChangeForUpdateStockLevel = Math.abs(transactionQuantity);
    } else if (type !== 'adjustment_cycle_count') {
      throw new Error(`Invalid transactionType: ${type}`);
    }
    // For 'adjustment_cycle_count', quantityChangeForUpdateStockLevel remains data.quantity (which is the delta)

    const newStock = previousStock + quantityChangeForUpdateStockLevel;

    if (data.itemType === 'Part' && newStock < 0 && type !== 'adjustment_cycle_count') {
        throw new Error(`Insufficient stock for part ${part?.partNumber}. Current: ${previousStock}, Requested change: ${quantityChangeForUpdateStockLevel}, results in ${newStock}`);
    }

    const newTransactionDoc = new InventoryTransaction({
      itemId: data.itemId,
      itemType: data.itemType,
      warehouseId: data.warehouseId,
      transactionType: type,
      quantity: transactionQuantity, // Store the actual quantity moved in the transaction log
      previousStock,
      newStock,
      transactionDate: data.transactionDate || new Date(),
      referenceNumber: data.referenceNumber || null,
      referenceType: data.referenceType || null,
      userId: data.userId,
      notes: data.notes || null,
    });
    const savedTransaction = await newTransactionDoc.save({ session });

    let updatedItem: IPartModel | null = null;
    if (data.itemType === 'Part') {
      updatedItem = await adjustStockLevelByDelta(data.itemId, data.warehouseId, quantityChangeForUpdateStockLevel, { session });
      if (!updatedItem) {
        throw new Error('Failed to update part stock level after creating transaction.');
      }
    }
    // If not a 'Part', updatedItem remains null, and stock adjustment is skipped for now.
    // The updateStockLevel service handles its own session for part update if needed, or we pass ours.
    // For atomicity, the part update should ideally be part of *this* session.
    // Let's assume updateStockLevel can accept a session or we modify it.
    // For now, if updateStockLevel doesn't use the passed session, this isn't fully atomic.
    // The current updateStockLevel does NOT accept a session. This is a design flaw for atomicity here.
    // We will proceed, but this atomicity concern should be logged or addressed in updateStockLevel.

    // The check for updatedItem (previously updatedPart) is now inside the conditional block above.

    await session.commitTransaction();
    session.endSession();

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transaction processed successfully in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: {
        transaction: savedTransaction,
        updatedItem: updatedItem 
      },
      meta: { duration }
    });

  } catch (error: any) {
    if (session) {
      await session.abortTransaction();
      session.endSession();
    }
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/inventory-transactions (${duration}ms):`, error);
    if (error.message.includes('Insufficient stock') || error.message.includes('Part not found') || error.message.includes('Item not found') || error.message.includes('Warehouse ID mismatch') || error.message.includes('Invalid transactionType') || error.message.includes('not yet fully supported')) {
        return NextResponse.json({ success: false, error: error.message }, { status: 400 });
    }
    const errorMessageString = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessageString || 'Failed to process inventory transaction' },
      { status: 500 } // Default to 500 as handleMongoDBError from lib/mongodb.ts returns string
    );
  }
}