import { NextRequest, NextResponse } from 'next/server';
import { 
  getInventoryById,
  manageInventoryAllocation,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import mongoose from 'mongoose';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

const ROUTE_PATH = '/api/inventory/allocate';

/**
 * POST handler for managing inventory allocations (reserve or release stock)
 * @param request - The incoming request with allocation data
 * @returns JSON response with the updated inventory record
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('POST', ROUTE_PATH, null, true);
  
  console.log('[API] POST /api/inventory/allocate - Managing inventory allocation');
  const allocationData = await request.json();

  // Basic validation
  if (!allocationData || typeof allocationData !== 'object') {
    throw new InvalidParameterError('Invalid allocation data provided');
  }
  
  // Validate required fields
  const requiredFields = ['id', 'allocation_quantity'];
  const missingFields = requiredFields.filter(field => !(field in allocationData));
  if (missingFields.length > 0) {
    throw new InvalidParameterError(`Missing required fields: ${missingFields.join(', ')}`, 
      missingFields.map(field => ({ field, message: 'This field is required' }))
    );
  }
  
  // Validate ID format
  const { id } = allocationData;
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new InvalidParameterError(
      `Invalid inventory record ID format: ${id}`,
      [{ field: 'id', message: 'Invalid ID format' }]
    );
  }
  
  // Check if inventory record exists and its item_type
  const inventoryRecord = await getInventoryById(id);
  if (!inventoryRecord) {
    return NextResponse.json(
      { data: null, error: `Inventory record with ID ${id} not found` },
      { status: 404 }
    );
  }

  // Block allocation for 'Part' types via this endpoint
  if (inventoryRecord.item_type === 'Part') {
    console.log(`[API] Attempted to manage allocation for 'Part' inventory record ${id} via legacy endpoint`);
    throw new InvalidParameterError(
      `Cannot manage allocation for Part inventory (ID: ${id}) via this endpoint. Part inventory and its availability are managed directly with the Part entity or through higher-level transaction processes.`, 
      [{ field: 'id', message: 'This inventory record refers to a Part, which has a different inventory management model.' }]
    );
  }
  
  // Validate allocation_quantity is a number
  if (typeof allocationData.allocation_quantity !== 'number') {
    throw new InvalidParameterError(
      'allocation_quantity must be a number',
      [{ field: 'allocation_quantity', message: 'Must be a number' }]
    );
  }

  console.log(`[API] Calling manageInventoryAllocation service with data: ${JSON.stringify(allocationData)}`);

  try {
    // Call the service function
    const updatedInventory = await manageInventoryAllocation(id, {
      allocation_quantity: allocationData.allocation_quantity,
      reference_number: allocationData.reference_number,
      notes: allocationData.notes
    });

    const duration = Date.now() - startTime;
    console.log(`[API] Service manageInventoryAllocation completed in ${duration}ms`);

    if (!updatedInventory) {
      return NextResponse.json(
        { data: null, error: `Failed to update allocation for inventory record with ID ${id}` },
        { status: 500 }
      );
    }

    return successResponse(
      updatedInventory,
      'Inventory allocation updated successfully',
      { 
        duration,
        previous_allocated: inventoryRecord.quantity_allocated,
        new_allocated: updatedInventory.quantity_allocated,
        allocation_change: allocationData.allocation_quantity
      }
    );
  } catch (error: any) {
    // Handle specific allocation errors
    if (error.message.includes('negative allocated quantity') || 
        error.message.includes('more than available quantity')) {
      return NextResponse.json(
        { data: null, error: error.message },
        { status: 400 }
      );
    }
    
    // Let the error handler middleware handle other errors
    throw error;
  }
}

// Apply the withErrorHandling middleware to our handler
export const POST = withErrorHandling(handlePOST, ROUTE_PATH); 