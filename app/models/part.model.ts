import mongoose, { Schema, Document, Types, Model } from 'mongoose';

// Interface for the Inventory sub-document as per database_schema_updated.md (lines 18-25)
export interface IInventory {
  currentStock: number;
  warehouseId: Types.ObjectId; // Refers to 'warehouses._id'
  safetyStockLevel: number;
  maximumStockLevel: number;
  averageDailyUsage: number; // Stored as Double in schema, Mongoose Number handles this
  abcClassification: string; // e.g., 'A', 'B', 'C'
  lastStockUpdate?: Date | null;
}

// Schema for the Inventory sub-document
const InventorySchema = new Schema<IInventory>({
  currentStock: { type: Number, required: true, min: 0 }, // Int32 in schema
  warehouseId: { type: Schema.Types.ObjectId, ref: 'Warehouse', required: true },
  safetyStockLevel: { type: Number, required: true }, // Int32 in schema
  maximumStockLevel: { type: Number, required: true }, // Int32 in schema
  averageDailyUsage: { type: Number, required: true }, // Double in schema
  abcClassification: { type: String, required: true }, // e.g., 'A', 'B', 'C'
  lastStockUpdate: { type: Date, default: null, required: false },
}, { _id: false });

// Interface for the plain Part data object
export interface IPart {
  partNumber: string;
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';
  inventory: IInventory;
  supplierId?: Types.ObjectId | null;
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: Types.ObjectId | null;
  createdAt?: Date;
  updatedAt?: Date;
}

// Interface for the Part Mongoose Document
export interface IPartDocument extends IPart, Document {
  // Inherits all fields from IPart (partNumber, name, description, etc.)
  // Inherits Mongoose document properties and methods from Document.
  // Schema options like timestamps: true will add createdAt and updatedAt.
  // _id is also automatically added by Mongoose schemas.
}

// Main Part Schema
const PartSchema: Schema<IPartDocument> = new Schema({
  partNumber: { type: String, required: true, unique: true, index: true, trim: true },
  name: { type: String, required: true, trim: true },
  description: { type: String, trim: true, default: null, required: false },
  technicalSpecs: { type: String, trim: true, default: null, required: false },
  isManufactured: { type: Boolean, default: false, required: true },
  reorderLevel: { type: Number, default: null, required: false }, // Int32 | Null
  status: {
    type: String,
    enum: ['active', 'inactive', 'obsolete'], // Canonical schema values
    default: 'active',
    required: true,
  },
  inventory: { type: InventorySchema, required: true },
  supplierId: { type: Schema.Types.ObjectId, ref: 'Supplier', default: null, required: false },
  unitOfMeasure: { type: String, required: true },
  costPrice: { type: Number, required: true }, // Double
  categoryId: { type: Schema.Types.ObjectId, ref: 'Category', default: null, required: false },
}, { timestamps: true });

// Ensure the model is not recompiled if it already exists
const Part: Model<IPartDocument> = mongoose.models.Part || mongoose.model<IPartDocument>('Part', PartSchema);

export { PartSchema }; // Exporting schema can be useful for subdocuments or extensions
export default Part;