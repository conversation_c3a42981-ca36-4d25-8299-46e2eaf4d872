import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Cost Data
interface ICostData {
  materialCost?: number | null;
  laborCost?: number | null;
  overheadCost?: number | null;
  totalCost?: number | null; // Can be derived or stored
  currency?: string | null;
}

// Schema for Cost Data
const CostDataSchema: Schema<ICostData> = new Schema({
  materialCost: { type: Number, default: null },
  laborCost: { type: Number, default: null },
  overheadCost: { type: Number, default: null },
  totalCost: { type: Number, default: null }, // Consider if this should be a virtual
  currency: { type: String, trim: true, default: 'USD' }, // Default currency
}, { _id: false });

// Interface for Parts Required in an Assembly (Canonical)
interface IAssemblyPartRequired {
  partId: Types.ObjectId; // Reference to parts._id
  quantity: number; // Canonical name
  unitOfMeasure?: string | null; // Canonical: optional
}

// Interface for Sub-Assemblies Required in an Assembly (Canonical)
interface ISubAssemblyRequired {
  subAssemblyId: Types.ObjectId; // Reference to assemblies._id (self-reference)
  quantity: number; // Canonical name
}

// Interface for Assembly document, aligned with the canonical schema
interface IAssembly extends Document {
  _id: Types.ObjectId;
  assemblyCode: string;      // Unique business code for the assembly, indexed
  name: string;              // Name of the assembly
  description?: string | null; // Optional description
  version: string;           // Canonical: Version as a string (e.g., "1.0.0", "revA")
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete' | 'archived'; // Canonical enum + archived
  // isTopLevel: boolean;    // Derived by checking if parentId is null. Not stored directly.
  parentId?: Types.ObjectId | null; // Reference to another assemblies._id if this is a sub-assembly
  partsRequired: IAssemblyPartRequired[]; // List of component parts and their quantities
  subAssemblies?: ISubAssemblyRequired[]; // List of sub-assemblies and their quantities
  manufacturingLeadTime?: string | null; // Canonical name, e.g., "2 hours", "3 days"
  // manufacturingInstructions?: string | null; // Removed as per canonical review
  costData?: ICostData | null; // Canonical: Nested cost data object
  notes?: string | null;      // Additional notes or comments
  createdBy: Types.ObjectId;   // User who created this assembly record, ref to users._id
  updatedBy: Types.ObjectId;   // User who last updated this assembly record, ref to users._id
  createdAt: Date;           // Timestamp of record creation
  updatedAt: Date;           // Timestamp of last record update
}

// Schema for Parts Required (Canonical)
const AssemblyPartRequiredSchema: Schema<IAssemblyPartRequired> = new Schema({
  partId: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    required: true,
  },
  quantity: { // Canonical name
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1'],
    validate: {
        validator: Number.isInteger,
        message: '{VALUE} is not an integer value for quantity'
    }
  },
  unitOfMeasure: { // Canonical: optional
    type: String,
    // required: false, // No longer required
    trim: true,
    default: null,
  },
}, { _id: false });

// Schema for Sub-Assemblies Required (Canonical)
const SubAssemblyRequiredSchema: Schema<ISubAssemblyRequired> = new Schema({
  subAssemblyId: {
    type: Schema.Types.ObjectId,
    ref: 'Assembly', // Self-reference to the Assembly model
    required: true,
  },
  quantity: { // Canonical name
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1'],
    validate: {
        validator: Number.isInteger,
        message: '{VALUE} is not an integer value for quantity'
    }
  },
}, { _id: false });

// Mongoose Schema for Assembly, aligned with the canonical schema
const AssemblySchema: Schema<IAssembly> = new Schema({
  assemblyCode: {
    type: String,
    required: [true, 'Assembly code is required.'],
    unique: true,
    index: true,
    trim: true,
  },
  name: {
    type: String,
    required: [true, 'Assembly name is required.'],
    trim: true,
    index: true,
  },
  description: {
    type: String,
    default: null,
    trim: true,
  },
  version: { // Canonical: string
    type: String,
    required: true,
    trim: true,
    // default: '1.0', // Default string version if applicable
  },
  status: { // Canonical enum + archived
    type: String,
    required: [true, 'Status is required.'],
    enum: {
      values: ['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'],
      message: 'Status must be one of: active, pending_review, design_phase, design_complete, obsolete, archived.',
    },
    default: 'design_phase',
    index: true,
  },
  // isTopLevel is removed, will be a virtual getter
  parentId: {
    type: Schema.Types.ObjectId,
    ref: 'Assembly',
    default: null,
  },
  partsRequired: {
    type: [AssemblyPartRequiredSchema],
    default: [],
  },
  subAssemblies: {
    type: [SubAssemblyRequiredSchema],
    default: [],
  },
  manufacturingLeadTime: { // Canonical name
    type: String,
    default: null,
    trim: true,
  },
  // manufacturingInstructions removed
  costData: { // Canonical: Nested cost data object
    type: CostDataSchema,
    default: null,
  },
  notes: {
    type: String,
    default: null,
    trim: true,
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User', // Ensure 'User' model is correctly named and imported if needed elsewhere
    required: true,
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, {
  timestamps: true, // Automatically manages createdAt and updatedAt
  // Add virtual for isTopLevel
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual for isTopLevel
AssemblySchema.virtual('isTopLevel').get(function(this: IAssembly) {
  return this.parentId === null || this.parentId === undefined;
});

// Create the Assembly model
const Assembly = mongoose.models.Assembly || mongoose.model<IAssembly>('Assembly', AssemblySchema);

// Export the model as default
export default Assembly;

// Export types with proper type exports for isolatedModules
export type { IAssembly, IAssemblyPartRequired, ISubAssemblyRequired, ICostData };
