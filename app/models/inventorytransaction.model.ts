import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for InventoryTransaction document
export interface IInventoryTransaction extends Document {
  _id: Types.ObjectId;
  itemId: Types.ObjectId;
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseId: Types.ObjectId;
  transactionType: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity: number;
  previousStock: number;
  newStock: number;
  transactionDate: Date;
  referenceNumber?: string | null;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | null;
  userId: Types.ObjectId;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for InventoryTransaction model
const InventoryTransactionSchema: Schema = new Schema(
  {
    itemId: {
      type: Schema.Types.ObjectId,
      refPath: 'itemType', // Dynamic reference based on itemType
      required: [true, 'Item ID is required'],
      index: true
    },
    itemType: {
      type: String,
      required: [true, 'Item type is required'],
      enum: {
        values: ['Part', 'Assembly', 'Product'],
        message: 'Item type must be one of: Part, Assembly, Product'
      },
      index: true
    },
    warehouseId: {
      type: Schema.Types.ObjectId,
      ref: 'Warehouse',
      required: [true, 'Warehouse ID is required'],
      index: true
    },
    transactionType: {
      type: String,
      required: [true, 'Transaction type is required'],
      enum: {
        values: ['stock_in_purchase', 'stock_out_production', 'adjustment_cycle_count', 'stock_in_production', 'transfer_out', 'transfer_in', 'sales_shipment'],
        message: 'Transaction type must be one of the predefined values'
      },
      index: true
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      validate: {
        validator: Number.isInteger,
        message: 'Quantity must be a whole number'
      }
    },
    previousStock: {
      type: Number,
      required: [true, 'Previous stock is required'],
      validate: {
        validator: Number.isInteger,
        message: 'Previous stock must be a whole number'
      }
    },
    newStock: {
      type: Number,
      required: [true, 'New stock is required'],
      validate: {
        validator: Number.isInteger,
        message: 'New stock must be a whole number'
      }
    },
    transactionDate: {
      type: Date,
      required: [true, 'Transaction date is required'],
      default: Date.now,
      index: true
    },
    referenceNumber: {
      type: String,
      default: null,
      index: true
    },
    referenceType: {
      type: String,
      enum: {
        values: ['PurchaseOrder', 'WorkOrder', 'SalesOrder', 'StockAdjustment', null],
        message: 'Reference type must be a valid model name or null'
      },
      default: null
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    notes: {
      type: String,
      default: null,
      trim: true
    }
  },
  { timestamps: true, collection: 'transactions' }
);

// Add indexes for efficient queries
InventoryTransactionSchema.index({ itemId: 1, itemType: 1, warehouseId: 1, transactionDate: -1 }); // Updated index

// Add validation to ensure consistent stock values
InventoryTransactionSchema.pre('validate', function(this: IInventoryTransaction & Document, next) {
  if (this.newStock !== this.previousStock + this.quantity) {
    this.invalidate('newStock', 'New stock must equal previous stock plus quantity');
  }
  next();
});

// Create and export InventoryTransaction model
const InventoryTransaction = mongoose.models.InventoryTransaction ||
  mongoose.model<IInventoryTransaction>('InventoryTransaction', InventoryTransactionSchema);

// Export as both named export and default export for compatibility
export { InventoryTransaction };
export default InventoryTransaction;