"use client";

import { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { Progress } from "@/app/components/ui/progress";
import { MoreHorizontal, Eye, Layers, Copy, PencilIcon, Trash2, ChevronRight, AlertCircle, BarChart4 } from "lucide-react";
import { cn } from "@/app/lib/utils";

import { DeleteAssemblyAction } from '@/app/components/actions/DeleteAssemblyAction';
import { QuickEditAssemblyAction } from '@/app/components/actions/QuickEditAssemblyAction';
import { DuplicateAssemblyAction } from '@/app/components/actions/DuplicateAssemblyAction';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';

import { AssembliesTableProps, Assembly, Part } from './types';
import { ExpandableRow } from './ExpandableRow';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Client component implementation of AssembliesTable
 * Handles all interactive logic and rendering
 */
export default function AssembliesTableClient({ assemblies, simple = false }: AssembliesTableProps) {
  const router = useRouter();
  const { theme } = useTheme();

  /**
   * Handle refresh after actions
   */
  const handleRefresh = () => {
    router.refresh();
  };

  // Log assemblies data for debugging
  useEffect(() => {
    console.log('[AssembliesTableClient] Received assemblies:', assemblies);
    if (assemblies.length > 0) {
      const firstAssembly = assemblies[0];
      console.log('[AssembliesTableClient] First assembly parts:', firstAssembly.partsRequired); // Updated field name

      // Check for assemblies with undefined parts
      const assembliesWithUndefinedParts = assemblies.filter(assembly => !assembly.partsRequired || !Array.isArray(assembly.partsRequired)); // Updated field name
      if (assembliesWithUndefinedParts.length > 0) {
        console.error('[AssembliesTableClient] Assemblies with undefined parts:',
          assembliesWithUndefinedParts.map(a => `${a.name} (${a.assemblyCode})`)) // Updated field name
      }

      // Check for missing part references - with null check
      const missingPartRefs = assemblies.flatMap(assembly => {
        if (!assembly.partsRequired || !Array.isArray(assembly.partsRequired)) { // Updated field name
          return [`${assembly.name} has no parts array`];
        }
        return assembly.partsRequired // Updated field name
          .filter(part => !part.partId) // Updated field name
          .map((_, index) => `${assembly.name} part #${index+1}`);
      });

      if (missingPartRefs.length > 0) {
        console.error('[AssembliesTableClient] Missing part references in:', missingPartRefs);
      }

      // Log parts with only one reference type for debugging - with null check (This check might be obsolete now)
      // const partialRefs = assemblies.flatMap(assembly => {
      //   if (!assembly.partsRequired || !Array.isArray(assembly.partsRequired)) return [];
      //   return assembly.partsRequired
      //     .filter(part => (part.partId && !part.part_id) || (!part.partId && part.part_id)) // This logic needs update if part_id is removed
      //     .map((part, index) => `${assembly.name} part #${index+1} has ${part.partId ? 'partId' : 'part_id'} only`);
      // });
      // if (partialRefs.length > 0) {
      //  console.log('[AssembliesTableClient] Parts with only one reference type:', partialRefs);
      // } // Restoring the closing brace, commenting out the log as the check is likely obsolete
    }
  }, [assemblies]);

  // No longer need the handleDelete function as we're using the DeleteAssemblyAction component

  if (assemblies.length === 0) {
    return (
      <div className="p-8 text-center">
        <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
        <p className="text-sm text-muted-foreground mb-4">
          There are no assemblies in the system yet.
        </p>
        <Button asChild>
          <Link href="/assemblies/create">Create Assembly</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="relative rounded-lg overflow-hidden">
      {/* Grid pattern background for enhanced styling */}
      <div className="absolute inset-0 pointer-events-none opacity-[0.03] dark:opacity-[0.07]">
        <div className="absolute inset-0 bg-grid-pattern-gray-400/30 [mask-image:linear-gradient(to_bottom,white,transparent)] bg-[length:20px_20px]" />
      </div>

      <div className={cn(
        "rounded-lg border backdrop-blur-[2px]",
        theme === 'light'
          ? "bg-white border-gray-200 shadow-sm"
          : "bg-[var(--T-bg-card)] border-border"
      )}>
        <Table>
          <TableHeader>
            <TableRow className={cn(
              theme === 'light'
                ? "bg-gray-50/80 border-gray-200"
                : "bg-[var(--T-bg-sidebar)] border-[var(--T-border-color)]"
            )}>
              <TableHead>Name</TableHead>
              <TableHead>Assembly Code</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Parts</TableHead>
              <TableHead>Inventory Status</TableHead>
              {!simple && <TableHead className="w-[70px] text-right">Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {assemblies.map((assembly) => {
              // Calculate inventory status
              const hasValidParts = assembly &&
                assembly.partsRequired &&
                Array.isArray(assembly.partsRequired) &&
                assembly.partsRequired.length > 0;

              const hasDetailedParts = hasValidParts &&
                (assembly.partsRequired || []).some(part =>
                  part && part.partDetails && typeof part.partDetails === 'object' && 'name' in part.partDetails
                );

              // Calculate inventory status
              const inventoryStatus = useMemo(() => {
                if (!hasDetailedParts || !hasValidParts) return { status: 'unknown', percent: 0 };

                let availableParts = 0;
                let totalPartsNeeded = 0;

                (assembly.partsRequired || []).forEach(part => {
                  if (!part || !part.partDetails) return; // Check for partDetails

                  const partDetail = part.partDetails; // Use partDetails
                  const quantity = part.quantityRequired || 1; // Use canonical quantityRequired
                  const available = partDetail?.inventory?.currentStock || 0;

                  totalPartsNeeded += quantity;
                  availableParts += Math.min(available, quantity);
                });

                if (totalPartsNeeded === 0) return { status: 'unknown', percent: 0 };

                const percent = Math.round((availableParts / totalPartsNeeded) * 100);

                if (percent >= 100) return { status: 'complete', percent: 100 };
                if (percent >= 75) return { status: 'good', percent };
                if (percent >= 30) return { status: 'warning', percent };
                return { status: 'critical', percent };
              }, [hasDetailedParts, hasValidParts, assembly]);

              // Get critical parts (low stock)
              const criticalParts = useMemo(() => {
                if (!hasDetailedParts || !hasValidParts) return [];

                return (assembly.partsRequired || [])
                  .filter(part => {
                    if (!part || !part.partDetails) return false; // Check for partDetails

                    const partDetail = part.partDetails; // Use partDetails
                    const quantity = part.quantityRequired || 1; // Use canonical quantityRequired
                    const available = partDetail?.inventory?.currentStock || 0;

                    return available < quantity;
                  })
                  .map(part => {
                    if (!part.partDetails) return null; // Should not happen due to filter, but good practice
                    const partDetail = part.partDetails; // Use partDetails
                    const quantity = part.quantityRequired || 1; // Use canonical quantityRequired
                    const available = partDetail?.inventory?.currentStock || 0;

                    return {
                      name: partDetail.name,
                      needed: quantity,
                      available,
                      shortage: quantity - available
                    };
                  })
                  .filter(Boolean) // Remove any nulls if partDetails was unexpectedly missing
                  .sort((a, b) => (b!.shortage / b!.needed) - (a!.shortage / a!.needed));
              }, [hasDetailedParts, hasValidParts, assembly]);

              return (
                <ExpandableRow
                  key={assembly._id}
                  assembly={assembly}
                  colSpan={simple ? 5 : 6}
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={(e) => e.stopPropagation()}>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      {simple ? (
                        assembly.name
                      ) : (
                        <Link
                          href={`/assemblies/${assembly._id}`}
                          className="hover:underline"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {assembly.name}
                        </Link>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-mono">
                      {assembly.assemblyCode} {/* Updated field name */}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <AssemblyStatusBadge assembly={assembly} size="sm" />
                  </TableCell>
                  <TableCell>
                    <PartsCountBadge assembly={assembly} size="sm" />
                  </TableCell>
                  <TableCell className="min-w-[150px]">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center justify-between gap-2">
                        <span className={cn(
                          "text-xs",
                          inventoryStatus.status === 'complete' ? "text-green-500" :
                          inventoryStatus.status === 'good' ? "text-blue-500" :
                          inventoryStatus.status === 'warning' ? "text-amber-500" :
                          inventoryStatus.status === 'critical' ? "text-red-500" : "text-muted-foreground"
                        )}>
                          {inventoryStatus.status === 'complete' ? "All Available" :
                          inventoryStatus.status === 'good' ? "Most Available" :
                          inventoryStatus.status === 'warning' ? "Some Missing" :
                          inventoryStatus.status === 'critical' ? "Critical Missing" : "Unknown"}
                        </span>
                        <span className="text-xs font-medium">{inventoryStatus.percent}%</span>
                      </div>
                      <Progress
                        value={inventoryStatus.percent}
                        className="h-1.5"
                        color={cn(
                          inventoryStatus.status === 'complete' ? "bg-green-500" :
                          inventoryStatus.status === 'good' ? "bg-blue-500" :
                          inventoryStatus.status === 'warning' ? "bg-amber-500" :
                          inventoryStatus.status === 'critical' ? "bg-red-500" : ""
                        )}
                      />
                      {criticalParts.length > 0 && (
                        <div className="mt-1 text-xs">
                          <span className="flex items-center text-red-500">
                            <AlertCircle className="mr-1 h-3 w-3" />
                            {criticalParts.length} part{criticalParts.length !== 1 ? 's' : ''} low
                          </span>
                        </div>
                      )}
                    </div>
                  </TableCell>

                  {!simple && (
                    <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                      <div className="flex items-center justify-end gap-1">
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/assemblies/${assembly._id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>

                        <QuickEditAssemblyAction
                          assembly={assembly}
                          onSuccess={handleRefresh}
                          variant="icon"
                          size="sm"
                          id={`quick-edit-${assembly._id}`}
                        />

                        <DuplicateAssemblyAction
                          assembly={assembly}
                          onSuccess={handleRefresh}
                          variant="icon"
                          size="sm"
                          id={`duplicate-${assembly._id}`}
                        />

                        <DeleteAssemblyAction
                          assembly={assembly}
                          onSuccess={handleRefresh}
                          variant="icon"
                          size="sm"
                          id={`delete-${assembly._id}`}
                        />

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/assemblies/${assembly._id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/assemblies/${assembly._id}/edit`}>
                                <PencilIcon className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const quickEditBtn = document.querySelector(`button[id^="quick-edit-${assembly._id}"]`) as HTMLButtonElement;
                              if (quickEditBtn) quickEditBtn.click();
                            }}>
                              <PencilIcon className="mr-2 h-4 w-4" />
                              Quick Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => {
                              const duplicateBtn = document.querySelector(`button[id^="duplicate-${assembly._id}"]`) as HTMLButtonElement;
                              if (duplicateBtn) duplicateBtn.click();
                            }}>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => {
                                const deleteBtn = document.querySelector(`button[id^="delete-${assembly._id}"]`) as HTMLButtonElement;
                                if (deleteBtn) deleteBtn.click();
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  )}
                </ExpandableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
