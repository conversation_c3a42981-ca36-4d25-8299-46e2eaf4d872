import mongoose, { Types } from 'mongoose';
import { InventoryTransaction, IInventoryTransaction } from '../models/inventorytransaction.model';
// import { Inventory } from '../models/inventory.model'; // Removed as Inventory model is no longer used here
import * as inventoryService from './inventory.service'; // This import might also be unused now, review later
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoryTransactionService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[InventoryTransactionService Error]', error);

  // Set Sentry tags for better filtering
  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'inventorytransaction');

  // Determine error type and set appropriate tags
  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  // Check for specific MongoDB error types
  if (error.name === 'ValidationError') {
    // Handle validation errors
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;

    // Set Sentry tags for validation errors
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    // Handle duplicate key errors
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;

    // Set Sentry tags for duplicate key errors
    setTag('error.subtype', 'duplicate_key');
  } else {
    // Handle other errors
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;

    // Set Sentry tags for general database errors
    setTag('error.subtype', 'general');
  }

  // Capture the exception in Sentry
  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  // Return standardized error response
  return { message: errorMessage, status: errorStatus };
};

/**
 * Fetch inventory transactions with pagination, sorting, and filtering options
 * @param options Pagination, sorting, and filtering options
 * @returns Transactions array and pagination information
 */
export async function fetchTransactions(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { transactionDate: -1 },
    filter = {}
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build the query
    const transactions = await InventoryTransaction.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('itemId') // Populate the item ID, specific fields depend on itemType
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .populate('userId', 'username') // Populate user who performed the transaction
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments(filter);

    logOperation('SUCCESS', {
      count: transactions.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch transactions');
  }
}

/**
 * Get a transaction by its ID
 * @param transactionId The ObjectId of the transaction
 * @returns The transaction document
 */
export async function getTransaction(transactionId: string) {
  logOperation('GET_BY_ID', { transactionId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(transactionId)) {
      throw new Error('Invalid transaction ID format');
    }

    // Find the transaction by ID
    const transaction = await InventoryTransaction.findById(transactionId)
      .populate('partId', 'partNumber name') // Populate related part basic info
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .populate('userId', 'username') // Populate user who performed the transaction
      .lean();

    if (!transaction) {
      logOperation('NOT_FOUND', { transactionId });
      return null;
    }

    logOperation('SUCCESS', { transactionId });
    return transaction;
  } catch (error: any) {
    logOperation('ERROR', { transactionId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transaction with ID ${transactionId}`);
  }
}

/**
 * Records an inventory transaction.
 * This function assumes stock levels are already updated by the calling service/API route.
 * @param itemId The ObjectId of the item (Part, Assembly, or Product).
 * @param itemType The type of the item ('Part', 'Assembly', 'Product').
 * @param warehouseId The ObjectId of the warehouse.
 * @param transactionType A string describing the type of transaction (e.g., 'Stock Update', 'Purchase Receipt').
 * @param quantityChange The numerical change in quantity (positive for additions, negative for deductions).
 * @param previousStockLevel The stock level *before* the transaction has been applied.
 * @param userId The ObjectId of the user performing the transaction.
 * @param newStockLevel The stock level *after* the transaction has been applied.
 * @param options Optional parameters, including a Mongoose ClientSession for transactions.
 * @returns The created inventory transaction document (IInventoryTransaction).
 * @throws Error if validation fails or if there's a database issue.
 */
export async function recordTransaction(
  itemId: string,
  itemType: 'Part' | 'Assembly' | 'Product',
  warehouseId: string,
  transactionType: string,
  quantityChange: number,
  previousStockLevel: number,
  userId: string,
  newStockLevel: number,
  options: { session?: mongoose.ClientSession } = {}
): Promise<IInventoryTransaction> {
  const { session } = options;
  logOperation('RECORD_TRANSACTION', { itemId, itemType, warehouseId, transactionType, quantityChange, userId, newStockLevel });

  await connectToMongoose();

  // Validate ObjectIds
  if (!Types.ObjectId.isValid(itemId) || 
      !Types.ObjectId.isValid(warehouseId) || 
      !Types.ObjectId.isValid(userId)) {
    logOperation('RECORD_TRANSACTION_INVALID_ID', { itemId, warehouseId, userId });
    throw new Error('Invalid Item ID, Warehouse ID, or User ID format.');
  }

  // Validate itemType
  if (!['Part', 'Assembly', 'Product'].includes(itemType)) {
    logOperation('RECORD_TRANSACTION_INVALID_ITEM_TYPE', { itemType });
    throw new Error(`Invalid itemType: ${itemType}. Must be 'Part', 'Assembly', or 'Product'.`);
  }
  
  // Validate quantityChange is a number
  if (typeof quantityChange !== 'number') {
      logOperation('RECORD_TRANSACTION_INVALID_QTY_CHANGE_TYPE', { quantityChange });
      throw new Error('quantityChange must be a number.');
  }

  // Validate newStockLevel is a number
  if (typeof newStockLevel !== 'number') {
    logOperation('RECORD_TRANSACTION_INVALID_NEW_STOCK_TYPE', { newStockLevel });
    throw new Error('newStockLevel must be a number.');
  }
  
  try {
    const newTransaction = new InventoryTransaction({
      itemId: new Types.ObjectId(itemId),
      itemType,
      warehouseId: new Types.ObjectId(warehouseId),
      transactionType,
      quantity: quantityChange,
      previousStock: previousStockLevel,
      newStock: newStockLevel,
      userId: new Types.ObjectId(userId),
      transactionDate: new Date(), // Default to now, can be overridden if needed by passing in DTO
    });

    await newTransaction.save({ session }); // Use the session passed in options

    logOperation('RECORD_TRANSACTION_SUCCESS', { transactionId: newTransaction._id, itemId, itemType });
    
    return newTransaction.toObject() as IInventoryTransaction;

  } catch (error: any) {
    logOperation('RECORD_TRANSACTION_ERROR', { itemId, itemType, error: error.message });
    const errDetails = handleMongoDBError(error); 
    throw new Error(errDetails.message || `Failed to record inventory transaction for item ${itemId} (Type: ${itemType})`);
  }
}

/**
 * Get transactions for a specific part
 * @param partId The ObjectId of the part
 * @param options Pagination, sorting options
 * @returns Transactions array and pagination information
 */
export async function getTransactionsByItem(itemId: string, options: any = {}) {
  logOperation('GET_BY_ITEM', { itemId, options });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(itemId)) {
      throw new Error('Invalid item ID format');
    }

    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 }
    } = options;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find transactions for the part
    const transactions = await InventoryTransaction.find({ itemId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username')
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments({ itemId });

    logOperation('SUCCESS', { 
      itemId, 
      count: transactions.length,
      totalCount
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { itemId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for item ID ${itemId}`);
  }
}

/**
 * Get transactions for a specific warehouse
 * @param warehouseId The ObjectId of the warehouse
 * @param options Pagination, sorting options
 * @returns Transactions array and pagination information
 */
export async function getTransactionsByWarehouse(warehouseId: string, options: any = {}) {
  logOperation('GET_BY_WAREHOUSE', { warehouseId, options });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(warehouseId)) {
      throw new Error('Invalid warehouse ID format');
    }

    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 }
    } = options;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find transactions for the warehouse
    const transactions = await InventoryTransaction.find({ warehouseId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('itemId') // Populate the item ID
      .populate('userId', 'username')
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments({ warehouseId });

    logOperation('SUCCESS', { 
      warehouseId, 
      count: transactions.length,
      totalCount
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { warehouseId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for warehouse ID ${warehouseId}`);
  }
}

/**
 * Get transactions by reference (e.g., PO, WO, SO)
 * @param referenceId The ObjectId of the reference document
 * @param referenceModel The model name of the reference
 * @returns Array of transactions related to the reference
 */
export async function getTransactionsByReference(referenceId: string, referenceModel: string) {
  logOperation('GET_BY_REFERENCE', { referenceId, referenceModel });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(referenceId)) {
      throw new Error('Invalid reference ID format');
    }

    // Find transactions for the reference
    const transactions = await InventoryTransaction.find({ 
      referenceId, 
      referenceModel 
    })
      .sort({ transactionDate: -1 })
      .populate('itemId') // Populate the item ID
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username')
      .lean();

    logOperation('SUCCESS', { 
      referenceId, 
      referenceModel, 
      count: transactions.length 
    });

    return transactions;
  } catch (error: any) {
    logOperation('ERROR', { referenceId, referenceModel, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for reference ID ${referenceId} of type ${referenceModel}`);
  }
} 