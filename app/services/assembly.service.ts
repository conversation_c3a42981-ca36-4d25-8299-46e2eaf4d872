import mongoose, { Types, Document } from 'mongoose';
import Assembly from '../models/assembly.model';
import type { IAssembly, IAssemblyPartRequired as ModelAssemblyPartRequired, ISubAssemblyRequired as ModelSubAssemblyRequired } from '../models/assembly.model';
import Part from '../models/part.model'; // Ensure Part model is imported for existence checks
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[AssemblyService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Error handler for MongoDB errors
 */
export function handleMongoDBError(error: any) {
  console.error('MongoDB Error:', error);

  try {
    setTag('error.type', 'mongodb');
    if (error.name === 'CastError') {
      setTag('mongodb.error.name', 'CastError');
      if (error.path) setTag('mongodb.error.path', error.path);
      if (error.value) setTag('mongodb.error.value', String(error.value).substring(0, 100));
      if (error.kind) setTag('mongodb.error.kind', error.kind);
    }
    captureException(error);
  } catch (sentryError) {
    console.error('Failed to report error to Sentry:', sentryError);
  }

  if (error.name === 'MongoServerError' && error.code === 11000) {
    const fieldName = Object.keys(error.keyPattern)[0];
    const fieldValue = error.keyValue[fieldName];
    return {
      type: 'duplicate',
      message: `The ${fieldName} '${fieldValue}' already exists. Please use a unique value.`,
      field: fieldName,
      value: fieldValue,
      code: 409
    };
  }

  if (error.name === 'ValidationError') {
    const validationErrors = Object.keys(error.errors).map(field => ({
      field,
      message: error.errors[field].message
    }));
    return {
      type: 'validation',
      message: 'Validation failed',
      errors: validationErrors,
      code: 400
    };
  }

  if (error.name === 'CastError') {
    return {
      type: 'cast_error',
      message: `Invalid data format: ${error.message}. Field: ${error.path}, Value: '${String(error.value)}', Expected Type: ${error.kind}.`,
      field: error.path,
      value: error.value,
      kind: error.kind,
      code: 400
    };
  }

  return {
    type: 'database',
    message: error.message || 'An unknown database error occurred',
    code: 500
  };
}

// Service interface for return types, extending IAssembly with Mongoose Document properties
export type IAssemblyService = IAssembly & Document;

// DTO-specific interfaces for part and sub-assembly requirements for API input
export interface AssemblyPartRequiredDto {
  partId: string; // Input as string from API, will be converted to ObjectId in service
  quantityRequired: number;
  unitOfMeasure?: string; // Optional, as per IAssemblyPartRequired
}

export interface AssemblySubAssemblyRequiredDto {
  subAssemblyId: string; // Input as string from API, will be converted to ObjectId in service
  quantityRequired: number;
}

// Canonical DTOs (Aligned with database_schema_updated.md)
interface CanonicalAssemblyPartRequiredDto {
  partId: string; // Input as string, converted to ObjectId in service
  quantity: number; // Canonical name
  unitOfMeasure?: string;
}

interface CanonicalSubAssemblyRequiredDto {
  subAssemblyId: string; // Input as string, converted to ObjectId in service
  quantity: number; // Canonical name
}

interface CanonicalCostDataDto {
  estimatedMaterialCost?: number | null;
  estimatedLaborCost?: number | null;
  estimatedOverheadCost?: number | null;
  totalEstimatedCost?: number | null;
}

export interface CanonicalCreateAssemblyDto {
  assemblyCode: string;
  name: string;
  description?: string | null;
  version: string; // Canonical: string
  status: 'design' | 'testing' | 'production' | 'obsolete'; // Values from canonical schema
  parentId?: string | null; // string from API, or null
  partsRequired?: CanonicalAssemblyPartRequiredDto[];
  subAssemblies?: CanonicalSubAssemblyRequiredDto[];
  manufacturingLeadTime?: string | null; // Canonical name
  costData?: CanonicalCostDataDto | null; // Canonical structure
  notes?: string | null;
  createdBy: string; // User ID string from API
}

export interface CanonicalUpdateAssemblyDto {
  name?: string;
  description?: string | null;
  version?: string; // Canonical: string
  status?: 'design' | 'testing' | 'production' | 'obsolete';
  parentId?: string | null;
  partsRequired?: CanonicalAssemblyPartRequiredDto[] | null;
  subAssemblies?: CanonicalSubAssemblyRequiredDto[] | null;
  manufacturingLeadTime?: string | null;
  costData?: CanonicalCostDataDto | null;
  notes?: string | null;
  updatedBy: string; // User ID string from API, required for updates
}

// DTO for creating an assembly
export interface CreateAssemblyDto {
  assemblyCode: string;
  name: string;
  description?: string | null;
  version: number; // Consider defaulting in service if not provided, or make optional
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete'; // Consider defaulting
  isTopLevel: boolean; // Consider defaulting based on parentId
  parentId?: string | null; // string from API, or null
  partsRequired?: AssemblyPartRequiredDto[];
  subAssemblies?: AssemblySubAssemblyRequiredDto[];
  estimatedManufacturingTime?: string | null;
  manufacturingInstructions?: string | null;
  assemblyCost?: number | null;
  notes?: string | null;
  createdBy: string; // string from API (user ID)
  updatedBy?: string; // Optional: User ID string from API, defaults to createdBy if not provided
}

// DTO for updating an assembly
export interface UpdateAssemblyDto {
  name?: string;
  description?: string | null;
  version?: number;
  status?: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete';
  isTopLevel?: boolean;
  parentId?: string | null; // string from API, null to clear parent, undefined to leave untouched
  partsRequired?: AssemblyPartRequiredDto[] | null; // Array to update, null to clear, undefined to leave untouched
  subAssemblies?: AssemblySubAssemblyRequiredDto[] | null; // Array to update, null to clear, undefined to leave untouched
  estimatedManufacturingTime?: string | null;
  manufacturingInstructions?: string | null;
  assemblyCost?: number | null;
  notes?: string | null;
  updatedBy: string; // Mandatory, string from API (user ID)
}

// Helper interfaces for canonical model data structures
interface ICanonicalModelAssemblyPartRequired {
  partId: Types.ObjectId;
  quantity: number; // Canonical name
  unitOfMeasure?: string;
}

interface ICanonicalModelSubAssemblyRequired {
  subAssemblyId: Types.ObjectId;
  quantity: number; // Canonical name
}

// Internal interface for data prepared for Mongoose model creation, aligned with canonical schema
interface IAssemblyCreationData {
  assemblyCode: string;
  name: string;
  description?: string | null;
  version: string; // Canonical: string
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete' | 'archived'; // Expanded status list
  parentId?: Types.ObjectId | null;
  partsRequired: ICanonicalModelAssemblyPartRequired[];
  subAssemblies: ICanonicalModelSubAssemblyRequired[];
  manufacturingLeadTime?: string | null; // Canonical name
  // manufacturingInstructions?: string | null; // Not in canonical schema, remove if not used
  costData?: CanonicalCostDataDto | null; // Canonical structure
  notes?: string | null;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId; // Should be same as createdBy on creation
  // isTopLevel is derived by the model based on parentId
}

// Internal interface for data prepared for Mongoose model update, aligned with canonical schema
interface IAssemblyUpdateData {
  name?: string;
  description?: string | null;
  version?: string; // Canonical: string
  status?: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete' | 'archived'; // Expanded status list
  parentId?: Types.ObjectId | null;
  partsRequired?: ICanonicalModelAssemblyPartRequired[];
  subAssemblies?: ICanonicalModelSubAssemblyRequired[];
  manufacturingLeadTime?: string | null; // Canonical name
  // manufacturingInstructions?: string | null; // Not in canonical schema, remove if not used
  costData?: CanonicalCostDataDto | null; // Canonical structure
  notes?: string | null;
  updatedBy: Types.ObjectId; // updatedBy is mandatory for an update operation
  updatedAt?: Date; // Added to align with service logic that sets updatedAt
  // isTopLevel is derived by the model based on parentId
}

/**
 * Fetches assemblies with pagination, sorting, and filtering.
 */
export async function getAllAssemblies(options: { page?: number; limit?: number; sort?: any; filter?: any; populateParts?: boolean } = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    populateParts = false,
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Create the aggregation pipeline
    const pipeline: any[] = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    if (populateParts) {
      // Unwind the partsRequired array to process each part individually
      pipeline.push({ $unwind: { path: "$partsRequired", preserveNullAndEmptyArrays: true } });
      // Perform a lookup to get the part details from the 'parts' collection
      pipeline.push({
        $lookup: {
          from: 'parts', // The actual name of the parts collection in MongoDB
          localField: 'partsRequired.partId',
          foreignField: '_id',
          as: 'partsRequired.partDetails'
        }
      });
      // Unwind the result of the lookup (partDetails will be an array, usually with one element)
      pipeline.push({ $unwind: { path: "$partsRequired.partDetails", preserveNullAndEmptyArrays: true } });
      // Group back the assembly documents, reconstructing the partsRequired array with populated details
      pipeline.push({
        $group: {
          _id: "$_id",
          // Add all original fields of the assembly document back
          // This needs to list all fields explicitly or use $$ROOT if appropriate and then merge
          doc: { $first: "$$ROOT" }, 
          partsRequired: { $push: "$partsRequired" }
        }
      });
      // Replace the root with the original document structure, merging populated partsRequired
      pipeline.push({
        $replaceRoot: {
          newRoot: { $mergeObjects: ["$doc", { partsRequired: "$partsRequired" }] }
        }
      });
      // After grouping, the sort order might be lost, so re-apply sort if necessary.
      // However, sorting before $group on _id should maintain order for $first.
      // If specific order of partsRequired within assembly is needed, that's another level of complexity.
      // For now, ensure the main sort is re-applied if it was disturbed.
      // Re-adding sort after $group and $replaceRoot to ensure final output is sorted as requested.
      // This might be inefficient for large datasets and could be optimized.
      // A common pattern is to sort after all data manipulation if the pipeline changes order.
      pipeline.push({ $sort: sort }); 
    }

    const assemblies = await Assembly.aggregate(pipeline);
    const totalCount = await Assembly.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: assemblies.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      assemblies,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    // Create a new error object that includes the code from errDetails
    const serviceError: any = new Error(errDetails.message || 'Failed to fetch assembly');
    serviceError.statusCode = errDetails.code || 500; // Attach the status code
    throw serviceError; // Throw this error
  }
}

/**
 * Gets a single assembly by its MongoDB ObjectId.
 */
export async function getAssemblyById(id: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ID', { id, includeParts });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    let assemblyQuery;
    
    if (includeParts) {
      // Populate part details when requested
      assemblyQuery = Assembly.findById(id)
        .populate({
          path: 'partsRequired.partId',
          model: 'Part',
          select: 'name partNumber description inventory category isAssembly'
        });
    } else {
      assemblyQuery = Assembly.findById(id);
    }
    
    const assembly = await assemblyQuery.lean() as IAssemblyService | null;
    
    if (!assembly) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', { id, includeParts });
    return assembly;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    const serviceError: any = new Error(errDetails.message || `Failed to get assembly by ID ${id}`);
    serviceError.statusCode = errDetails.code || 500;
    throw serviceError;
  }
}

/**
 * Gets a single assembly by its assemblyCode.
 */
export async function getAssemblyByAssemblyCode(assemblyCode: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ASSEMBLY_CODE', { assemblyCode, includeParts });
  console.log(`[AssemblyService] Looking up assembly by assemblyCode: "${assemblyCode}", includeParts: ${includeParts}`);
  
  try {
    await connectToMongoose();
    // console.log(`[AssemblyService] DB connected. Querying with { assemblyCode: "${assemblyCode}" }`);

    let assembly: IAssemblyService | null = null;
    let queryPathDescription = '';

    // --- Attempt 1: Find by assemblyCode --- 
    queryPathDescription = `assemblyCode: "${assemblyCode}"`;
    let assemblyQuery = Assembly.findOne({ assemblyCode: assemblyCode });
    if (includeParts) {
      console.log(`[AssemblyService] [${queryPathDescription}] Populating partsRequired.partId`);
      assemblyQuery = assemblyQuery.populate({
          path: 'partsRequired.partId',
          model: 'Part',
          select: 'name partNumber description inventory category isAssembly'
        });
    }
    assembly = await assemblyQuery.lean() as IAssemblyService | null;
    // console.log(`[AssemblyService] [${queryPathDescription}] Found assembly (pre-population or no parts):`, assembly ? assembly._id : 'null');

    // --- Attempt 2: Find by assembly_id (fallback for backward compatibility) --- 
    if (!assembly) {
      queryPathDescription = `assembly_id: "${assemblyCode}"`;
      console.log(`[AssemblyService] Not found with assemblyCode, trying ${queryPathDescription}`);
      let fallbackQuery = Assembly.findOne({ assembly_id: assemblyCode });
      if (includeParts) {
        console.log(`[AssemblyService] [${queryPathDescription}] Populating partsRequired.partId`);
        fallbackQuery = fallbackQuery.populate({
            path: 'partsRequired.partId',
            model: 'Part',
            select: 'name partNumber description inventory category isAssembly'
          });
      }
      assembly = await fallbackQuery.lean() as IAssemblyService | null;
      // console.log(`[AssemblyService] [${queryPathDescription}] Found assembly (pre-population or no parts):`, assembly ? assembly._id : 'null');
    }
    
    if (!assembly) {
      logOperation('GET_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
      console.log(`[AssemblyService] Assembly with code/ID "${assemblyCode}" not found after all attempts.`);
      return null;
    }
    
    // console.log(`[AssemblyService] Final assembly object for "${assemblyCode}":`, JSON.stringify(assembly, null, 2));
    logOperation('GET_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode, id: assembly._id, includeParts });
    return assembly;

  } catch (error: any) {
    logOperation('GET_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: error.message, stack: error.stack });
    console.error(`[AssemblyService] Error fetching assembly by code "${assemblyCode}":`, error);
    // Let the centralized Sentry error handler in the API route or middleware catch this
    // Re-throwing the original error or a new error with context
    const errDetails = handleMongoDBError(error); // This function already reports to Sentry
    const serviceError: any = new Error(errDetails.message || `Failed to get assembly by code ${assemblyCode}. Original error: ${error.message}`);
    serviceError.statusCode = errDetails.code || 500;
    throw serviceError;
  }
}

// For backward compatibility, alias the function

/**
 * Creates a new assembly.
 */
export async function createAssembly(assemblyData: CanonicalCreateAssemblyDto): Promise<IAssemblyService> {
  const { assemblyCode, name, createdBy } = assemblyData;
  logOperation('CREATE_ASSEMBLY_START', { assemblyCode, name, createdBy });

  if (!assemblyCode || !name || !createdBy) {
    logOperation('CREATE_ASSEMBLY_ERROR', { assemblyCode, name, error: 'Missing required fields: assemblyCode, name, createdBy' });
    const err = new Error('Assembly code, name, and createdBy are required.');
    (err as any).statusCode = 400;
    throw err;
  }
  if (!mongoose.Types.ObjectId.isValid(createdBy)) {
    logOperation('CREATE_ASSEMBLY_ERROR', { assemblyCode, name, createdBy, error: 'Invalid createdBy ID format' });
    const err = new Error('Invalid createdBy user ID format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    await connectToMongoose();

    const existingAssembly = await Assembly.findOne({ assemblyCode }).lean();
    if (existingAssembly) {
      logOperation('CREATE_ASSEMBLY_ERROR', { assemblyCode, error: 'Duplicate assembly code' });
      const err = new Error(`Assembly with code ${assemblyCode} already exists.`);
      (err as any).statusCode = 409; // Conflict
      throw err;
    }

    // Define structure for partsRequired that aligns with canonical schema (using 'quantity')
    const processedPartsRequired: { partId: Types.ObjectId; quantity: number; unitOfMeasure?: string }[] = [];
    if (assemblyData.partsRequired && assemblyData.partsRequired.length > 0) {
      for (const partReq of assemblyData.partsRequired) {
        if (!mongoose.Types.ObjectId.isValid(partReq.partId)) {
          throw new Error(`Invalid partId format: ${partReq.partId}`);
        }
        if (typeof partReq.quantity !== 'number' || partReq.quantity <= 0 || !Number.isInteger(partReq.quantity)) {
          throw new Error(`Quantity for part ${partReq.partId} must be a positive integer.`);
        }
        const partObjectId = new Types.ObjectId(partReq.partId);
        const partExists = await Part.findById(partObjectId).select('_id').lean();
        if (!partExists) {
          throw new Error(`Part with ID ${partReq.partId} not found.`);
        }
        // unitOfMeasure is optional in CanonicalAssemblyPartRequiredDto.
        // If the Assembly model (IAssemblyPartRequired) makes unitOfMeasure required, this could be an issue.
        // Assuming task-480 aligns the model to also make unitOfMeasure optional or handles its absence.
        processedPartsRequired.push({
          partId: partObjectId,
          quantity: partReq.quantity, // Using .quantity from Canonical DTO
          unitOfMeasure: partReq.unitOfMeasure, // Optional string
        });
      }
    }

    // Define structure for subAssemblies that aligns with canonical schema (using 'quantity')
    const processedSubAssemblies: { subAssemblyId: Types.ObjectId; quantity: number }[] = [];
    if (assemblyData.subAssemblies && assemblyData.subAssemblies.length > 0) {
      for (const subReq of assemblyData.subAssemblies) {
        if (!mongoose.Types.ObjectId.isValid(subReq.subAssemblyId)) {
          throw new Error(`Invalid subAssemblyId format: ${subReq.subAssemblyId}`);
        }
        if (typeof subReq.quantity !== 'number' || subReq.quantity <= 0 || !Number.isInteger(subReq.quantity)) {
          throw new Error(`Quantity for sub-assembly ${subReq.subAssemblyId} must be a positive integer.`);
        }
        const subAssemblyObjectId = new Types.ObjectId(subReq.subAssemblyId);
        const subAssemblyExists = await Assembly.findById(subAssemblyObjectId).select('_id').lean();
        if (!subAssemblyExists) {
          throw new Error(`Sub-assembly with ID ${subReq.subAssemblyId} not found.`);
        }
        processedSubAssemblies.push({
          subAssemblyId: subAssemblyObjectId,
          quantity: subReq.quantity, // Using .quantity from Canonical DTO
        });
      }
    }

    let parentObjectId: Types.ObjectId | null = null;
    if (assemblyData.parentId) {
      if (!mongoose.Types.ObjectId.isValid(assemblyData.parentId)) {
        throw new Error(`Invalid parentId format: ${assemblyData.parentId}`);
      }
      parentObjectId = new Types.ObjectId(assemblyData.parentId);
      const parentExists = await Assembly.findById(parentObjectId).select('_id').lean();
      if (!parentExists) {
        throw new Error(`Parent assembly with ID ${assemblyData.parentId} not found.`);
      }
      if (processedSubAssemblies.some(sa => sa.subAssemblyId.equals(parentObjectId))) {
        throw new Error('A sub-assembly cannot also be the parent assembly in the same definition.');
      }
    }

    // Construct the payload for the new Assembly document, aligning with canonical schema names
    // Assumes IAssembly model (after task-480) will align with these fields/types.
    const newAssemblyPayload = {
      assemblyCode: assemblyData.assemblyCode,
      name: assemblyData.name,
      description: assemblyData.description === undefined ? null : assemblyData.description,
      version: assemblyData.version, // string, as per canonical
      status: assemblyData.status, // string, as per canonical
      parentId: parentObjectId,
      partsRequired: processedPartsRequired, // Uses 'quantity'
      subAssemblies: processedSubAssemblies, // Uses 'quantity'
      manufacturingLeadTime: assemblyData.manufacturingLeadTime === undefined ? null : assemblyData.manufacturingLeadTime,
      costData: assemblyData.costData === undefined ? null : assemblyData.costData,
      notes: assemblyData.notes === undefined ? null : assemblyData.notes,
      createdBy: new Types.ObjectId(createdBy),
      updatedBy: new Types.ObjectId(createdBy), // Set updatedBy to createdBy for new assembly
      // isTopLevel will be derived by Mongoose model virtual or pre-save hook if needed, or not stored if purely derivative
    };

    // Type assertion for newAssemblyPayload if IAssembly is not yet fully aligned by task-480
    const newAssembly = new Assembly(newAssemblyPayload as any); 
    await newAssembly.save();
    logOperation('CREATE_ASSEMBLY_SUCCESS', { assemblyId: newAssembly._id.toString(), assemblyCode });
    return newAssembly as IAssemblyService;

  } catch (error: any) {
    logOperation('CREATE_ASSEMBLY_MONGO_ERROR', { assemblyCode, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode
    
    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to create assembly ${assemblyCode}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  } // End of catch (error: any)
} // End of export async function createAssembly

/**
 * Updates an existing assembly by its assemblyCode.
 */
export async function updateAssemblyByAssemblyCode(assemblyCode: string, updateData: CanonicalUpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE_BY_ASSEMBLY_CODE_START', { assemblyCode, updatedBy: updateData.updatedBy });

  if (!assemblyCode || typeof assemblyCode !== 'string' || assemblyCode.trim() === '') {
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: 'Assembly code must be a non-empty string.' });
    const err = new Error('Assembly code must be a non-empty string.');
    (err as any).statusCode = 400;
    throw err;
  }

  if (!updateData.updatedBy || !mongoose.Types.ObjectId.isValid(String(updateData.updatedBy))){
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, updatedBy: updateData.updatedBy, error: 'Valid updatedBy field is required.' });
    const err = new Error('Valid updatedBy field is required for updates.');
    (err as any).statusCode = 400;
    throw err;
  }
  
  // Prevent updates to immutable fields (assemblyCode and createdBy are not in CanonicalUpdateAssemblyDto)
  // No explicit check needed here if DTO definition enforces this.

  try {
    await connectToMongoose();
    const assembly = await Assembly.findOne({ assemblyCode }); 

    if (!assembly) {
      logOperation('UPDATE_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
      return null; // Or throw 404 error
    }

    // Process partsRequired if present in updateData
    // Assuming ModelAssemblyPartRequired will align with 'quantity' and optional 'unitOfMeasure'
    if (updateData.partsRequired === null) {
        assembly.partsRequired = [];
    } else if (Array.isArray(updateData.partsRequired)) {
      assembly.partsRequired = await Promise.all(
        updateData.partsRequired.map(async (partReqDto): Promise<any> => { // Using 'any' temporarily, should align with ModelAssemblyPartRequired
          if (!partReqDto.partId || !mongoose.Types.ObjectId.isValid(String(partReqDto.partId))) {
            throw new Error(`Invalid partId format in partsRequired: ${partReqDto.partId}`);
          }
          const partObjectId = new Types.ObjectId(String(partReqDto.partId));
          const partExists = await Part.findById(partObjectId).select('_id').lean();
          if (!partExists) {
            throw new Error(`Part with ID ${partObjectId.toString()} not found during update.`);
          }
          if (typeof partReqDto.quantity !== 'number' || partReqDto.quantity <= 0 || !Number.isInteger(partReqDto.quantity)) {
            throw new Error(`Quantity for part ${partReqDto.partId} must be a positive integer.`);
          }
          // unitOfMeasure is optional in CanonicalAssemblyPartRequiredDto
          return {
            partId: partObjectId,
            quantity: partReqDto.quantity, // Using .quantity from Canonical DTO
            unitOfMeasure: partReqDto.unitOfMeasure, // Optional string
          };
        })
      );
    } // If undefined, partsRequired is not modified

    // Process subAssemblies if present in updateData
    // Assuming ModelSubAssemblyRequired will align with 'quantity'
    if (updateData.subAssemblies === null) {
        assembly.subAssemblies = [];
    } else if (Array.isArray(updateData.subAssemblies)) {
      assembly.subAssemblies = await Promise.all(
        updateData.subAssemblies.map(async (subReqDto): Promise<any> => { // Using 'any' temporarily, should align with ModelSubAssemblyRequired
          if (!subReqDto.subAssemblyId || !mongoose.Types.ObjectId.isValid(String(subReqDto.subAssemblyId))) {
            throw new Error(`Invalid subAssemblyId format in subAssemblies: ${subReqDto.subAssemblyId}`);
          }
          const subAssemblyObjectId = new Types.ObjectId(String(subReqDto.subAssemblyId));
          if (subAssemblyObjectId.equals(assembly._id)) {
            throw new Error('An assembly cannot be its own sub-assembly.');
          }
          const subAssemblyExists = await Assembly.findById(subAssemblyObjectId).select('_id').lean();
          if (!subAssemblyExists) {
            throw new Error(`Sub-assembly with ID ${subAssemblyObjectId.toString()} not found during update.`);
          }
          if (typeof subReqDto.quantity !== 'number' || subReqDto.quantity <= 0 || !Number.isInteger(subReqDto.quantity)) {
            throw new Error(`Quantity for sub-assembly ${subReqDto.subAssemblyId} must be a positive integer.`);
          }
          return {
            subAssemblyId: subAssemblyObjectId,
            quantity: subReqDto.quantity, // Using .quantity from Canonical DTO
          };
        })
      );
    } // If undefined, subAssemblies is not modified

    // Handle parentId. isTopLevel is not in CanonicalUpdateAssemblyDto and should be derived or handled by model.
    if (updateData.hasOwnProperty('parentId')) {
      if (updateData.parentId === null) {
        assembly.parentId = null;
      } else if (updateData.parentId && mongoose.Types.ObjectId.isValid(String(updateData.parentId))) {
        const parentObjectId = new Types.ObjectId(String(updateData.parentId));
        if (parentObjectId.equals(assembly._id)) {
          throw new Error('An assembly cannot be its own parent.');
        }
        const parentExists = await Assembly.findById(parentObjectId).select('_id').lean();
        if (!parentExists) {
          throw new Error(`Parent assembly with ID ${updateData.parentId} not found.`);
        }
        assembly.parentId = parentObjectId;
      } else if (updateData.parentId !== undefined) { // parentId is present but not null and not valid ObjectId string
        throw new Error(`Invalid parentId format: ${updateData.parentId}`);
      }
      // Implicitly, isTopLevel might change based on parentId. Model should handle this if it's a virtual or pre-save logic.
    }

    // Apply other updatable fields from CanonicalUpdateAssemblyDto
    // Explicitly list fields to update to ensure type safety and adherence to the DTO.
    if (updateData.hasOwnProperty('name')) assembly.name = updateData.name!;
    if (updateData.hasOwnProperty('description')) assembly.description = updateData.description;
    if (updateData.hasOwnProperty('version')) assembly.version = updateData.version!;
    if (updateData.hasOwnProperty('status')) {
        if (!['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'].includes(updateData.status!)){
            logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, status: updateData.status, error: 'Invalid status value' });
            throw new Error(`Invalid status value: ${updateData.status}. Must be one of 'active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'.`);
        }
        assembly.status = updateData.status!;
    }
    if (updateData.hasOwnProperty('manufacturingLeadTime')) assembly.manufacturingLeadTime = updateData.manufacturingLeadTime;
    if (updateData.hasOwnProperty('costData')) assembly.costData = updateData.costData;
    if (updateData.hasOwnProperty('notes')) assembly.notes = updateData.notes;
    // 'updatedBy' is handled below. 'partsRequired', 'subAssemblies', 'parentId' handled above.

    assembly.updatedBy = new Types.ObjectId(String(updateData.updatedBy));
    assembly.updatedAt = new Date();

    await assembly.save();
    logOperation('UPDATE_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode: assembly.assemblyCode, assemblyId: assembly._id.toString() });
    return assembly as IAssemblyService;
  } catch (error: any) {
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode

    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to update assembly ${assemblyCode}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  }
}

// For backward compatibility
export const updateAssemblyByAssemblyId = updateAssemblyByAssemblyCode;

/**
 * Deletes an assembly by its MongoDB ObjectId.
 */
/**
 * Updates an existing assembly by its MongoDB ObjectId.
 */
export async function updateAssembly(id: string, updateData: CanonicalUpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE_ASSEMBLY_BY_ID_START', { assemblyId: id, updatedBy: updateData.updatedBy });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', { assemblyId: id, error: 'Invalid assembly ID format' });
    const err = new Error('Invalid assembly ID format');
    (err as any).statusCode = 400;
    throw err;
  }
  
  if (!updateData.updatedBy || !mongoose.Types.ObjectId.isValid(String(updateData.updatedBy))) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', { assemblyId: id, updatedBy: updateData.updatedBy, error: 'Valid updatedBy field is required.' });
    const err = new Error('Valid updatedBy field (ObjectId string) is required for updates.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    await connectToMongoose();

    const updatePayload: Record<string, any> = { // Using Record<string, any> to accommodate canonical fields
      updatedBy: new Types.ObjectId(String(updateData.updatedBy)),
      updatedAt: new Date(), // Consistently add updatedAt
    };

    // Assign fields from CanonicalUpdateAssemblyDto if they exist
    if (updateData.hasOwnProperty('name')) updatePayload.name = updateData.name;
    if (updateData.hasOwnProperty('description')) updatePayload.description = updateData.description;
    if (updateData.hasOwnProperty('version')) updatePayload.version = updateData.version; // version is string
    
    if (updateData.hasOwnProperty('status')) {
      const validStatuses = ['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'];
      if (!validStatuses.includes(updateData.status!)) {
        logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', { assemblyId: id, status: updateData.status, error: 'Invalid status value' });
        const err = new Error(`Invalid status value: ${updateData.status}. Must be one of '${validStatuses.join("', '")}'.`);
        (err as any).statusCode = 400;
        throw err;
      }
      updatePayload.status = updateData.status;
    }
    
    if (updateData.hasOwnProperty('manufacturingLeadTime')) updatePayload.manufacturingLeadTime = updateData.manufacturingLeadTime;
    if (updateData.hasOwnProperty('costData')) updatePayload.costData = updateData.costData; // costData is an object
    if (updateData.hasOwnProperty('notes')) updatePayload.notes = updateData.notes;

    // Process partsRequired
    if (updateData.hasOwnProperty('partsRequired')) {
      if (updateData.partsRequired === null) {
        updatePayload.partsRequired = [];
      } else if (Array.isArray(updateData.partsRequired)) {
        updatePayload.partsRequired = await Promise.all(
          updateData.partsRequired.map(async (partReqDto) => {
            if (!partReqDto.partId || !mongoose.Types.ObjectId.isValid(String(partReqDto.partId))) {
              const err = new Error(`Invalid partId format in partsRequired: ${partReqDto.partId}`);
              (err as any).statusCode = 400;
              throw err;
            }
            const partObjectId = new Types.ObjectId(String(partReqDto.partId));
            const partExists = await Part.findById(partObjectId).select('_id').lean();
            if (!partExists) {
              const err = new Error(`Part with ID ${partObjectId.toString()} not found.`);
              (err as any).statusCode = 404;
              throw err;
            }
            if (typeof partReqDto.quantity !== 'number' || partReqDto.quantity <= 0 || !Number.isInteger(partReqDto.quantity)) {
              const err = new Error(`Quantity for part ${partReqDto.partId} must be a positive integer.`);
              (err as any).statusCode = 400;
              throw err;
            }
            // This structure should align with ModelAssemblyPartRequired or a canonical equivalent
            return { 
              partId: partObjectId, 
              quantity: partReqDto.quantity, // Canonical: quantity
              unitOfMeasure: partReqDto.unitOfMeasure // Optional in canonical DTO
            };
          })
        );
      }
    }

    // Process subAssemblies
    if (updateData.hasOwnProperty('subAssemblies')) {
      if (updateData.subAssemblies === null) {
        updatePayload.subAssemblies = [];
      } else if (Array.isArray(updateData.subAssemblies)) {
        updatePayload.subAssemblies = await Promise.all(
          updateData.subAssemblies.map(async (subReqDto) => {
            if (!subReqDto.subAssemblyId || !mongoose.Types.ObjectId.isValid(String(subReqDto.subAssemblyId))) {
              const err = new Error(`Invalid subAssemblyId format in subAssemblies: ${subReqDto.subAssemblyId}`);
              (err as any).statusCode = 400;
              throw err;
            }
            const subAssemblyObjectId = new Types.ObjectId(String(subReqDto.subAssemblyId));
            if (subAssemblyObjectId.equals(id)) {
              const err = new Error(`Assembly ${id} cannot contain itself as a sub-assembly.`);
              (err as any).statusCode = 400;
              throw err;
            }
            const subAssemblyExists = await Assembly.findById(subAssemblyObjectId).select('_id').lean();
            if (!subAssemblyExists) {
              const err = new Error(`Sub-assembly with ID ${subAssemblyObjectId.toString()} not found.`);
              (err as any).statusCode = 404;
              throw err;
            }
            if (typeof subReqDto.quantity !== 'number' || subReqDto.quantity <= 0 || !Number.isInteger(subReqDto.quantity)) {
              const err = new Error(`Quantity for sub-assembly ${subReqDto.subAssemblyId} must be a positive integer.`);
              (err as any).statusCode = 400;
              throw err;
            }
            // This structure should align with ModelSubAssemblyRequired or a canonical equivalent
            return { 
              subAssemblyId: subAssemblyObjectId, 
              quantity: subReqDto.quantity // Canonical: quantity
            };
          })
        );
      }
    }

    // Process parentId
    if (updateData.hasOwnProperty('parentId')) {
      if (updateData.parentId === null) {
        updatePayload.parentId = null;
      } else if (updateData.parentId && mongoose.Types.ObjectId.isValid(String(updateData.parentId))) {
        const parentObjectId = new Types.ObjectId(String(updateData.parentId));
        if (parentObjectId.equals(id)) {
          const err = new Error('An assembly cannot be its own parent.');
          (err as any).statusCode = 400;
          throw err;
        }
        const parentExists = await Assembly.findById(parentObjectId).select('_id').lean();
        if (!parentExists) {
          const err = new Error(`Parent assembly with ID ${updateData.parentId} not found.`);
          (err as any).statusCode = 404;
          throw err;
        }
        updatePayload.parentId = parentObjectId;
      } else if (updateData.parentId !== undefined) { // parentId is present but not null and not valid ObjectId string
        const err = new Error(`Invalid parentId format: ${updateData.parentId}`);
        (err as any).statusCode = 400;
        throw err;
      }
      // isTopLevel is not in CanonicalUpdateAssemblyDto, should be derived by model if needed
    }

    const updatedAssemblyDoc = await Assembly.findByIdAndUpdate(
      id,
      { $set: updatePayload }, 
      { new: true, runValidators: true, context: 'query' }
      // Removed .lean() to get a Mongoose document
    );

    if (!updatedAssemblyDoc) {
      logOperation('UPDATE_ASSEMBLY_BY_ID_NOT_FOUND', { assemblyId: id });
      const err = new Error(`Assembly with ID ${id} not found.`);
      (err as any).statusCode = 404;
      throw err; // Throw error instead of returning null for consistency
    }

    logOperation('UPDATE_ASSEMBLY_BY_ID_SUCCESS', { assemblyId: updatedAssemblyDoc._id.toString(), assemblyCode: updatedAssemblyDoc.assemblyCode });
    return updatedAssemblyDoc as IAssemblyService; // Cast to IAssemblyService, should be compatible now

  } catch (error: any) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_MONGO_ERROR', { assemblyId: id, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode

    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to update assembly ${id}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  }
}

/**
 * Deletes an assembly by its MongoDB ObjectId.
 */
export async function deleteAssembly(id: string): Promise<boolean> {
  logOperation('DELETE', { id });

  if (!mongoose.Types.ObjectId.isValid(id)) {
    logOperation('DELETE_INVALID_ID_FORMAT', { id });
    throw new Error('Invalid ID format for deletion.');
  }

  try {
    await connectToMongoose();

    // Check if assembly is referenced by another assembly as a parent
    const referencingAssemblyAsParent = await Assembly.findOne({ parentId: new Types.ObjectId(id) }).lean<IAssembly>();
    if (referencingAssemblyAsParent) {
      logOperation('DELETE_REFERENCED_AS_PARENT', { id, referencedBy: referencingAssemblyAsParent._id });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${referencingAssemblyAsParent.name} (${referencingAssemblyAsParent.assemblyCode}).`);
    }

    // Check if assembly is referenced as a subAssembly in other assemblies
    const referencingAssemblyAsSubAssembly = await Assembly.findOne({ 'subAssemblies.subAssemblyId': new Types.ObjectId(id) }).lean<IAssembly>();
    if (referencingAssemblyAsSubAssembly) {
      logOperation('DELETE_REFERENCED_AS_SUBASSEMBLY', { id, referencedBy: referencingAssemblyAsSubAssembly._id });
      throw new Error(`Cannot delete assembly: it is used as a sub-assembly in assembly ${referencingAssemblyAsSubAssembly.name} (${referencingAssemblyAsSubAssembly.assemblyCode}).`);
    }

    const result = await Assembly.deleteOne({ _id: new Types.ObjectId(id) });

    if (result.deletedCount === 0) {
      logOperation('DELETE_NOT_FOUND', { id });
      // Consider if this should throw an error or return false. Returning false for not found is common.
      return false; 
    }
    
    logOperation('DELETE_SUCCESS', { id, deletedCount: result.deletedCount });
    return true;
  } catch (error: any) {
    logOperation('DELETE_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    // Ensure a clear message is thrown, especially if it's a custom one from our checks
    if (error.message.startsWith('Cannot delete assembly:')) {
        throw error; 
    }
    throw new Error(errDetails.message || `Failed to delete assembly with id ${id}.`);
  }
}

/**
 * Deletes an assembly by its assemblyCode.
 */
export async function deleteAssemblyByAssemblyCode(assemblyCode: string): Promise<boolean> {
  logOperation('DELETE_BY_ASSEMBLY_CODE', { assemblyCode });
  
  try {
    await connectToMongoose();

    // Find the assembly first to check references
    const assembly = await Assembly.findOne({ 
      $or: [
        { assemblyCode: assemblyCode },
        { assembly_id: assemblyCode }
      ]
    });
    
    if (!assembly) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
      return false;
    }
    
    // Check if assembly is referenced by another assembly
    const isReferenced = await Assembly.findOne({ parentId: assembly._id });
    if (isReferenced) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_REFERENCED', { 
        assemblyCode, 
        referencedBy: isReferenced.assemblyCode || isReferenced.assembly_id 
      });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${isReferenced.name} (${isReferenced.assemblyCode || isReferenced.assembly_id})`);
    }

    const result = await Assembly.deleteOne({ _id: assembly._id });
    
    logOperation('DELETE_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode, deletedCount: result.deletedCount });
    return result.deletedCount > 0;
  } catch (error: any) {
    logOperation('DELETE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete assembly with assemblyCode ${assemblyCode}`);
  }
}

// For backward compatibility
export const deleteAssemblyByAssemblyId = deleteAssemblyByAssemblyCode;

/**
 * Searches assemblies based on a text query string.
 */
export async function searchAssemblies(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { assemblyCode: new RegExp(query, 'i') },
        { assembly_id: new RegExp(query, 'i') }, // For backward compatibility
        { description: new RegExp(query, 'i') }
      ];
      
      // If query matches notes field
      if ('notes' in searchFilter) {
        searchFilter.$or.push({ notes: new RegExp(query, 'i') });
      }
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search assemblies');
  }
}

/**
 * Gets assemblies by status with pagination.
 */
export async function getAssembliesByStatus(status: string, options: any = {}) {
  logOperation('GET_BY_STATUS', { status });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Combine status filter with additional filters
    const searchFilter = { 
      ...filter,
      status
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_STATUS_SUCCESS', { status, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('GET_BY_STATUS_ERROR', { status, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get assemblies with status ${status}`);
  }
}