import mongoose, { Model, Types, ClientSession } from 'mongoose';
// import { InjectModel } from '@nestjs/mongoose'; // Example if using NestJS
import Part, { IPart, IPartDocument, IInventory } from '../models/part.model';
import Assembly from '../models/assembly.model';
import type { IAssembly } from '../models/assembly.model';
import connectToMongoose from '../lib/mongodb';
import { deletePart } from './mongodb'; // Import deletePart from mongodb.ts
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[PartService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[PartService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'part');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// import { PartNotFoundException, DuplicatePartNumberException, InvalidStockOperationException } from '../common/exceptions/part.exceptions.ts'; // Custom exceptions to be defined

// Local IPart interface removed, using IPart and IPartDocument from part.model.ts directly.

// SCHEMA ALIGNMENT: Updated all DTOs and logic to use canonical field names from database_schema_updated.md. Legacy/incorrect field names removed.
// Updated DTO to match the new schema
export interface CreatePartDto {
  partNumber: string;
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete'; // Aligned with part.model.ts
  inventory: {
    currentStock: number;
    warehouseId: string; // Will be converted to ObjectId
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null; // Optional, will be set if not provided during creation
  };
  supplierId?: string | null; // Will be converted to ObjectId
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: string | null; // Will be converted to ObjectId
}

// Define which inventory fields are updatable via a general PUT
export interface UpdatePartInventoryDto {
  safetyStockLevel?: number;
  maximumStockLevel?: number;
  averageDailyUsage?: number;
  abcClassification?: string;
  // warehouseId is not updatable via this general PUT to avoid complexity.
  // currentStock and lastStockUpdate are managed by transactions/system.
}

// UpdatePartDto allows partial updates to allowed fields.
// It omits fields that should be immutable or system-managed (e.g., partNumber, createdAt, updatedAt)
// or fields within inventory that are managed differently (e.g., currentStock).
export interface UpdatePartDto {
  name?: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured?: boolean;
  reorderLevel?: number | null;
  status?: 'active' | 'inactive' | 'obsolete';
  inventory?: UpdatePartInventoryDto;
  supplierId?: string | null;
  unitOfMeasure?: string;
  costPrice?: number;
  categoryId?: string | null;
}

// This structure uses exported functions rather than a class, 
// aligning with the existing code snippet provided for this file.
// If a class-based service is preferred, that can be adjusted.

/**
 * Creates a new part.
 */
export async function createPart(partData: CreatePartDto): Promise<IPartDocument> {
  logOperation('CREATE', { partNumber: partData.partNumber });
  await connectToMongoose();
  try {
    const existingPart = await Part.findOne({ partNumber: partData.partNumber }).exec();
    if (existingPart) {
      throw new Error(`Part with number ${partData.partNumber} already exists.`); // Consider custom error
    }

    // Ensure all inventory fields are present as per IInventory, providing defaults if necessary
    const inventoryData: IInventory = {
      currentStock: partData.inventory.currentStock,
      warehouseId: new Types.ObjectId(partData.inventory.warehouseId),
      safetyStockLevel: partData.inventory.safetyStockLevel,
      maximumStockLevel: partData.inventory.maximumStockLevel,
      averageDailyUsage: partData.inventory.averageDailyUsage,
      abcClassification: partData.inventory.abcClassification,
      lastStockUpdate: partData.inventory.lastStockUpdate || new Date(), // Set lastStockUpdate if not provided
    };

    const partToSave = new Part({
      ...partData, // Spreads all top-level fields from DTO
      inventory: inventoryData, // Assign the fully constructed inventory object
      supplierId: partData.supplierId ? new Types.ObjectId(partData.supplierId) : null,
      categoryId: partData.categoryId ? new Types.ObjectId(partData.categoryId) : null,
      // Ensure status from DTO is used, it's already part of ...partData
    });

    const savedPart = await partToSave.save();
    logOperation('CREATE_SUCCESS', { partId: savedPart._id, partNumber: savedPart.partNumber });
    return savedPart as IPartDocument;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { partNumber: partData.partNumber, error: error.message });
    if (error.message.includes('already exists')) {
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate duplicate error for consistent handling
        throw new Error(errDetails.message || `Part with number ${partData.partNumber} already exists.`);
    }
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create part');
  }
}

/**
 * Retrieves a part by its MongoDB ObjectId.
 */
export async function getPartById(id: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_ID', { id });
  await connectToMongoose();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }
  try {
    const part = await Part.findById(id)
      .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
      .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
      .populate({ path: 'categoryId', select: 'name' })
      .lean(); // Using lean for performance, if full Mongoose doc isn't needed
    if (!part) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    logOperation('GET_BY_ID_SUCCESS', { id });
    return part as IPartDocument; // Cast if lean() is used and IPartDocument is expected
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    handleMongoDBError(error);
    return null; // Or rethrow, depending on desired error handling strategy
  }
}

/**
 * Retrieves a part by its unique partNumber.
 */
export async function getPartByPartNumberService(partNumber: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_PARTNUMBER', { partNumber });
  await connectToMongoose();
  try {
    const part = await Part.findOne({ partNumber })
      .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
      .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
      .populate({ path: 'categoryId', select: 'name' })
      .lean(); // Using lean for performance
    if (!part) {
      logOperation('GET_BY_PARTNUMBER_NOT_FOUND', { partNumber });
      return null;
    }
    logOperation('GET_BY_PARTNUMBER_SUCCESS', { partNumber });
    return part as IPartDocument; // Cast if lean() is used and IPartDocument is expected
  } catch (error: any) {
    logOperation('GET_BY_PARTNUMBER_ERROR', { partNumber, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Retrieves all parts, possibly with filters and pagination.
 */
export async function getAllParts(options: any = {}): Promise<{ parts: IPart[], pagination: any }> {
  const {
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {}
  } = options;
  logOperation('GET_ALL', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });
  await connectToMongoose();
  try {
    const skip = (page - 1) * limit;
    const partsQuery = Part.find(filter)
      .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
      .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
      .populate({ path: 'categoryId', select: 'name' })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    const [parts, totalCount] = await Promise.all([
      partsQuery.exec() as any as Promise<IPart[]>,
      Part.countDocuments(filter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    logOperation('GET_ALL_SUCCESS', { count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('GET_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch all parts');
  }
}

/**
 * Updates an existing part.
 */
export async function updatePartService(id: string, updateData: UpdatePartDto): Promise<IPartDocument | null> {
  logOperation('UPDATE', { partId: id, updateData });
  await connectToMongoose();

  if (!mongoose.Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_INVALID_ID', { partId: id });
    throw new Error('Invalid Part ID format'); // Consider custom error
  }

  try {
    // Prepare a mutable copy of updateData for modifications
    const updatePayload: any = { ...updateData };

    // Convert string IDs to ObjectIds for inventory and other references if they exist in updateData
    if (updatePayload.inventory && updatePayload.inventory.warehouseId && typeof updatePayload.inventory.warehouseId === 'string') {
      updatePayload.inventory.warehouseId = new Types.ObjectId(updatePayload.inventory.warehouseId);
    }
    if (updatePayload.supplierId && typeof updatePayload.supplierId === 'string') {
      updatePayload.supplierId = new Types.ObjectId(updatePayload.supplierId);
    }
    if (updatePayload.categoryId && typeof updatePayload.categoryId === 'string') {
      updatePayload.categoryId = new Types.ObjectId(updatePayload.categoryId);
    }
    
    // If inventory is part of the update and specific stock levels are changing, update lastStockUpdate
    // This is a general update, specific stock changes should ideally use updateStockLevel or a dedicated inventory service method.
    if (updatePayload.inventory && (
        updatePayload.inventory.currentStock !== undefined || 
        updatePayload.inventory.safetyStockLevel !== undefined ||
        updatePayload.inventory.maximumStockLevel !== undefined ||
        updatePayload.inventory.averageDailyUsage !== undefined ||
        updatePayload.inventory.abcClassification !== undefined
        )) {
        // If any part of inventory object is being updated, set lastStockUpdate
        updatePayload.inventory.lastStockUpdate = new Date();
    }

    const updatedPart = await Part.findByIdAndUpdate(id, updatePayload, { new: true, runValidators: true }).exec();
    if (!updatedPart) {
      logOperation('UPDATE_NOT_FOUND', { partId: id });
      throw new Error(`Part not found with id ${id}, unable to update.`);
    }
    logOperation('UPDATE_SUCCESS', { partId: id, updatedFields: Object.keys(updateData) });
    return updatedPart as IPartDocument | null;
  } catch (error: any) {
    logOperation('UPDATE_ERROR', { partId: id, error: error.message });
    // Example of specific error handling, adapt as needed
    if (error.code === 11000 || (error.message && error.message.includes('duplicate key'))) { 
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate specific error for handler
        throw new Error(errDetails.message || 'A data conflict occurred: a unique identifier may already exist.');
    }
    const errDetails = handleMongoDBError(error); // General handler
    throw new Error(errDetails.message || `Failed to update part ${id}`);
  }
}

/**
 * Deletes a part by its ObjectId, utilizing the comprehensive deletePart logic from mongodb.ts.
 */
export async function deletePartService(id: string): Promise<void> {
  logOperation('SERVICE_DELETE_PART_START', { partId: id });
  await connectToMongoose(); // Ensure connection

  if (!Types.ObjectId.isValid(id)) {
    logOperation('SERVICE_DELETE_PART_INVALID_ID', { partId: id });
    const err = new Error('Invalid Part ID format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    // Call the centralized deletePart function from mongodb.ts
    const result = await deletePart(id);

    if (!result.success) {
      logOperation('SERVICE_DELETE_PART_FAILED_FROM_DB', { partId: id, message: result.message, statusCode: result.statusCode });
      const error = new Error(result.message);
      (error as any).statusCode = result.statusCode || 500;
      throw error;
    }

    logOperation('SERVICE_DELETE_PART_SUCCESS', { partId: id });
    // On successful deletion, no specific document is returned by deletePart from mongodb.ts
  } catch (error: any) {
    if (error.statusCode) {
        logOperation('SERVICE_DELETE_PART_ERROR_WITH_STATUS', { partId: id, message: error.message, statusCode: error.statusCode });
        throw error; // Re-throw the error with its existing statusCode
    }
    
    logOperation('SERVICE_DELETE_PART_ERROR_UNHANDLED', { partId: id, originalError: error.message });
    // Using the local handleMongoDBError for now
    const errDetails = handleMongoDBError(error);
    const newError = new Error(errDetails.message || `Failed to delete part ${id}`);
    (newError as any).statusCode = errDetails.status || 500;
    throw newError;
  }
}

/**
 * Adjusts the stock level of a specific part's embedded inventory.
 * Validates that the transaction warehouseId matches the part's embedded inventory warehouseId.
 */
export async function adjustStockLevelByDelta(
  itemId: string,
  warehouseId: string,
  quantityChange: number,
  options: { session?: ClientSession } = {}
): Promise<IPart | null> {
  const { session } = options;
  logOperation('ADJUST_STOCK_DELTA', { itemId, quantityChange, warehouseId });
  await connectToMongoose();

  if (!mongoose.Types.ObjectId.isValid(itemId) || !mongoose.Types.ObjectId.isValid(warehouseId)) {
    logOperation('ADJUST_STOCK_DELTA_INVALID_ID', { itemId, warehouseId });
    throw new Error('Invalid Item ID or Warehouse ID format for stock adjustment');
  }

  try {
    const part = await Part.findById(itemId).session(session || null).exec();
    if (!part) {
      logOperation('ADJUST_STOCK_DELTA_PART_NOT_FOUND', { itemId });
      throw new Error(`Part not found with id ${itemId} for stock adjustment`);
    }

    // Ensure the part has an inventory sub-document
    if (!part.inventory || !part.inventory.warehouseId) {
      logOperation('ADJUST_STOCK_DELTA_NO_INVENTORY', { itemId });
      throw new Error(`Part ${itemId} does not have embedded inventory information or a warehouse ID.`);
    }

    if (part.inventory.warehouseId.toString() !== warehouseId) {
      logOperation('ADJUST_STOCK_DELTA_WAREHOUSE_MISMATCH', { 
        itemId, 
        partWarehouse: part.inventory.warehouseId.toString(), 
        txWarehouse: warehouseId 
      });
      throw new Error(
        `Warehouse ID mismatch: Part ${itemId} is stocked in warehouse ${part.inventory.warehouseId}, but transaction is for warehouse ${warehouseId}.`
      );
    }

    part.inventory.currentStock += quantityChange;
    part.inventory.lastStockUpdate = new Date();
    
    // Optional: Validate stock not negative if business rule requires
    // if (part.inventory.currentStock < 0) {
    //   // Abort transaction if session is provided
    //   if (session && session.inTransaction()) {
    //     await session.abortTransaction();
    //   }
    //   logOperation('ADJUST_STOCK_DELTA_NEGATIVE_STOCK', { itemId, currentStock: part.inventory.currentStock });
    //   throw new Error('Stock level cannot become negative.'); 
    // }

    const updatedPart = await part.save({ session });
    logOperation('ADJUST_STOCK_DELTA_SUCCESS', { itemId, newStock: updatedPart.inventory.currentStock });
    return updatedPart.toObject() as IPart;
  } catch (error: any) {
    logOperation('ADJUST_STOCK_DELTA_ERROR', { itemId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to adjust stock for part ${itemId}`);
  }
}

/**
 * Checks if a part is below its reorder level.
 * Updated to use the new schema structure with inventory sub-document
 */
export async function checkReorderPoint(partId: string): Promise<boolean> {
  logOperation('CHECK_REORDER_POINT', { partId });
  const part = await getPartById(partId); // Uses the service function already defined
  if (!part || part.reorderLevel === null || part.reorderLevel === undefined || typeof part.inventory?.currentStock !== 'number') {
    logOperation('CHECK_REORDER_POINT_NOT_APPLICABLE', { partId, reorderLevel: part?.reorderLevel, currentStock: part?.inventory?.currentStock });
    return false;
  }
  const needsReorder = part.inventory.currentStock < part.reorderLevel;
  logOperation('CHECK_REORDER_POINT_RESULT', { partId, needsReorder });
  return needsReorder;
}

/**
 * Search for parts with text search and filtering options
 */
export async function searchParts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    let searchFilter: any = { ...filter };
    
    if (query) {
      searchFilter.$text = { $search: query };
    }

    const partsQuery = Part.find(searchFilter)
      .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
      .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
      .populate({ path: 'categoryId', select: 'name' })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const [parts, totalCount] = await Promise.all([
      partsQuery.exec() as any as Promise<IPart[]>,
      Part.countDocuments(searchFilter)
    ]);

    const pagination = {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search parts');
  }
} 

// Unit Test Considerations:
// - Mock connectToMongoose, Part model methods (findOne, findById, save, etc.)
// - Test successful CRUD operations.
// - Test error handling: duplicate partNumber, validation errors, part not found, invalid IDs.
// - Test business logic: stock updates, reorder point checks.
// - Test pagination and filtering in getAllParts and searchParts.

// Export an instance of the service if not using DI framework like NestJS, or export the class itself.
// export const partService = new PartService(); 