import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import AssemblyFormContent from '../AssemblyFormContent';
import { toast } from 'sonner';

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

jest.mock('@/app/contexts/AssemblyFormContext', () => {
  const originalModule = jest.requireActual('@/app/contexts/AssemblyFormContext');
  
  return {
    ...originalModule,
    useAssemblyForm: jest.fn(() => ({
      formData: {
        name: 'Test Assembly',
        assemblyCode: 'TEST-001',
        description: 'Test Description',
        status: 'active',
        partsRequired: [
          {
            _id: '1',
            partId: {
              _id: 'PART-001',
              name: 'Test Part',
              description: 'Test Part Description',
              partNumber: 'TEST-PART-001',
            },
            quantityRequired: 1,
            unitOfMeasure: 'ea',
          },
        ],
        productId: null,
        parentId: null,
        isTopLevel: true,
        version: 1,
        manufacturingInstructions: null,
        estimatedBuildTime: null,
      },
      isLoading: false,
      isSaving: false,
      isEditing: false,
      isDirty: true,
      setFormData: jest.fn(),
      updateFormField: jest.fn(),
      resetForm: jest.fn(),
      saveAssembly: jest.fn().mockResolvedValue(true),
      saveAssemblyWithFreshData: jest.fn().mockResolvedValue(true),
      addPart: jest.fn(),
      updatePart: jest.fn(),
      removePart: jest.fn(),
      loadAssembly: jest.fn().mockResolvedValue({}),
      refreshStockData: jest.fn().mockResolvedValue(undefined),
      checkStockStaleness: jest.fn(() => ({
        hasStaleData: false,
        staleParts: [],
        recommendations: []
      })),
      autoRefreshStockIfNeeded: jest.fn().mockResolvedValue(true),
      getStockRefreshRecommendation: jest.fn(() => ({
        shouldRefresh: false,
        reasons: [],
        priority: 'low' as const
      })),
    })),
    AssemblyFormProvider: ({ children }: { children: React.ReactNode }) => (
      <>{children}</>
    ),
  };
});

describe('AssemblyFormContent', () => {
  it('renders the form with initial data', () => {
    render(<AssemblyFormContent />);
    
    // Check that key elements are rendered
    expect(screen.getByText('Create New Assembly')).toBeInTheDocument();
    expect(screen.getByText('Define assembly parts, quantities, and details')).toBeInTheDocument();
  });
  
  it('validates input before submission', async () => {
    const { useAssemblyForm } = require('@/app/contexts/AssemblyFormContext');
    useAssemblyForm.mockReturnValueOnce({
      formData: {
        name: '',
        assembly_id: '',
        description: '',
        assembly_stage: 'Final Assembly',
        parts: [],
      },
      isLoading: false,
      isSaving: false,
      isEditing: false,
      isDirty: true,
      saveAssembly: jest.fn().mockResolvedValue(true),
      resetForm: jest.fn(),
    });
    
    render(<AssemblyFormContent />);
    
    // Find the save button and click it
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    // Check that validation errors were shown
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Assembly name is required');
    });
  });
  
  it('handles successful save', async () => {
    const { useAssemblyForm } = require('@/app/contexts/AssemblyFormContext');
    const saveAssemblyMock = jest.fn().mockResolvedValue(true);
    
    useAssemblyForm.mockReturnValueOnce({
      formData: {
        name: 'Test Assembly',
        assembly_id: 'TEST-001',
        description: 'Test Description',
        assembly_stage: 'Final Assembly',
        parts: [
          {
            _id: '1',
            partId: {
              _id: 'PART-001',
              name: 'Test Part',
              description: 'Test Part Description',
            },
            quantityRequired: 1,
          },
        ],
      },
      isLoading: false,
      isSaving: false,
      isEditing: false,
      isDirty: true,
      saveAssembly: saveAssemblyMock,
      resetForm: jest.fn(),
    });
    
    render(<AssemblyFormContent />);
    
    // Find the save button and click it
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    // Check that saveAssembly was called
    await waitFor(() => {
      expect(saveAssemblyMock).toHaveBeenCalled();
    });
  });
}); 