import { NextRequest, NextResponse } from 'next/server';
import withDatabase from './withDatabase';
import { handleApiError } from '@/app/lib/api-error-handler';

/**
 * Middleware that combines database connection and error handling
 * @param handler - The API route handler
 * @param routePath - The API route path (for error logging and tagging)
 * @returns A wrapped handler with database connection and error handling
 */
export default function withErrorHandling(
  handler: (request: NextRequest, context?: { params: any }) => Promise<NextResponse>,
  routePath: string
) {
  // First wrap with database connection middleware
  // We assume withDatabase.js can handle the context argument or will ignore it if not designed for it.
  // The main goal is for withErrorHandling to pass it to the original handler.
  const handlerWithDb = withDatabase(handler as any); // Use 'as any' to bypass strict type checking for withDatabase.js
  
  // Then wrap with error handling
  return async function enhancedHandler(request: NextRequest, context?: { params: any }) {
    const startTime = Date.now();
    console.log(`[withErrorHandling] Request received for: ${request.method} ${request.nextUrl.pathname} (Route: ${routePath})`);
    try {
      // Call the handler with database connection, passing context
      const response = await handlerWithDb(request, context);
      console.log(`[withErrorHandling] Handler for ${request.method} ${request.nextUrl.pathname} completed. Status: ${response.status}`);
      return response;
    } catch (error) {
      console.error(`[withErrorHandling] Error in handler for ${request.method} ${request.nextUrl.pathname}:`, error);
      // Handle any errors using our standardized handler
      return await handleApiError(error, routePath, startTime);
    }
  };
} 