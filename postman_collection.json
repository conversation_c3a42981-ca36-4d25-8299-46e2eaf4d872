{"info": {"_postman_id": "f5a3cdd8-c6e0-4b1f-8a7b-9c2d1e0f6a3b", "name": "Trend_IMS API", "description": "API endpoints for the Trend_IMS application. Endpoints derived from route.ts files. Placeholder HTTP methods for uninspected routes require verification. Example request bodies and path variables have been updated with more realistic placeholders (e.g., example ObjectIds); REPLACE THESE WITH ACTUAL DATA from your database for successful operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "/api/analytics/inventory-trends", "item": [{"name": "Get Inventory Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/analytics/inventory-trends", "host": ["{{baseUrl}}"], "path": ["api", "analytics", "inventory-trends"]}}, "response": []}]}, {"name": "/api/assemblies/:id", "item": [{"name": "Get Assembly by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assemblies/:id", "host": ["{{baseUrl}}"], "path": ["api", "assemblies", ":id"], "variable": [{"key": "id", "value": "681f796bd6a21248b8ec7640"}]}}, "response": []}, {"name": "Update Assembly by ID", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"name\": \"Updated Tamping Arm Assembly\", \"status\": \"active\", \"version\": 2, \"manufacturingInstructions\": \"Follow SOP-ASM-100 Rev B.\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assemblies/:id", "host": ["{{baseUrl}}"], "path": ["api", "assemblies", ":id"], "variable": [{"key": "id", "value": "681f796bd6a21248b8ec7640"}]}}, "response": []}, {"name": "Patch Assembly by ID", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\"estimatedBuildTime\": \"1.75 hours\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assemblies/:id", "host": ["{{baseUrl}}"], "path": ["api", "assemblies", ":id"], "variable": [{"key": "id", "value": "681f796bd6a21248b8ec7640"}]}}, "response": []}, {"name": "Delete Assembly by ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/assemblies/:id", "host": ["{{baseUrl}}"], "path": ["api", "assemblies", ":id"], "variable": [{"key": "id", "value": "681f796bd6a21248b8ec7640"}]}}, "response": []}]}, {"name": "/api/assemblies", "item": [{"name": "Get All Assemblies", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assemblies", "host": ["{{baseUrl}}"], "path": ["api", "assemblies"]}}, "response": []}, {"name": "Create Assembly", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"assemblyCode\": \"ASM-SAMPLE-001\", \"name\": \"New Sample Assembly\", \"isTopLevel\": true, \"partsRequired\": [{\"partId\": \"6640f0a0a1b2c3d4e5f6a00a\", \"quantityRequired\": 1, \"unitOfMeasure\": \"pcs\"}], \"status\": \"active\", \"version\": 1}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assemblies", "host": ["{{baseUrl}}"], "path": ["api", "assemblies"]}}, "response": []}]}, {"name": "/api/parts/:id", "item": [{"name": "Get Part by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/parts/:id", "host": ["{{baseUrl}}"], "path": ["api", "parts", ":id"], "variable": [{"key": "id", "value": "6640f0a0a1b2c3d4e5f6a00a"}]}}, "response": []}, {"name": "Update Part by ID", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"name\": \"Spacer Ring (Updated)\", \"costPrice\": 9.50, \"inventory\": {\"currentStock\": 175, \"safetyStockLevel\": 15}}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/parts/:id", "host": ["{{baseUrl}}"], "path": ["api", "parts", ":id"], "variable": [{"key": "id", "value": "6640f0a0a1b2c3d4e5f6a00a"}]}}, "response": []}, {"name": "Delete Part by ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/parts/:id", "host": ["{{baseUrl}}"], "path": ["api", "parts", ":id"], "variable": [{"key": "id", "value": "6640f0a0a1b2c3d4e5f6a00a"}]}}, "response": []}]}, {"name": "/api/parts", "item": [{"name": "Get All Parts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/parts", "host": ["{{baseUrl}}"], "path": ["api", "parts"]}}, "response": []}, {"name": "Create Part", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"partNumber\": \"PN-SAMPLE-002\", \"name\": \"New Sample Part\", \"description\": \"A detailed sample part for testing\", \"isManufactured\": false, \"status\": \"active\", \"inventory\": {\"currentStock\": 50, \"warehouseId\": \"65f000000000000000000001\", \"safetyStockLevel\": 5, \"maximumStockLevel\": 100, \"averageDailyUsage\": 1, \"abcClassification\": \"B\"}, \"supplierId\": \"681f796ad6a21248b8ec75ff\", \"unitOfMeasure\": \"pcs\", \"costPrice\": 25.99, \"categoryId\": \"65f000020000000000000001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/parts", "host": ["{{baseUrl}}"], "path": ["api", "parts"]}}, "response": []}]}, {"name": "/api/purchase-orders/:id", "item": [{"name": "Get Purchase Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders/:id", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", ":id"], "variable": [{"key": "id", "value": "PO-2025-001"}]}}, "response": []}, {"name": "Update Purchase Order by ID", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"status\": \"partially_received\", \"notes\": \"Updated: Partial shipment received.\", \"items\": [{\"partId\": \"6640f0a0a1b2c3d4e5f6a001\", \"description\": \"Roller bearing Plasser & Theurer/SKF/FAG make\", \"quantity\": 50, \"unitPrice\": 70.00, \"lineTotal\": 3500.00, \"receivedQuantity\": 25}]}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/purchase-orders/:id", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", ":id"], "variable": [{"key": "id", "value": "PO-2025-001"}]}}, "response": []}, {"name": "Delete Purchase Order by ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders/:id", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", ":id"], "variable": [{"key": "id", "value": "PO-2025-001"}]}}, "response": []}]}, {"name": "/api/purchase-orders", "item": [{"name": "Get All Purchase Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"]}}, "response": []}, {"name": "Create Purchase Order", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"poNumber\": \"PO-SAMPLE-002\", \"supplierId\": \"681f796ad6a21248b8ec75ff\", \"orderDate\": \"2025-06-10T00:00:00.000Z\", \"expectedDeliveryDate\": \"2025-06-25T00:00:00.000Z\", \"items\": [{\"partId\": \"6640f0a0a1b2c3d4e5f6a00a\", \"description\": \"Spacer Ring for new PO\", \"quantity\": 10, \"unitPrice\": 8.90, \"lineTotal\": 89.00, \"receivedQuantity\": 0}], \"totalAmount\": 89.00, \"status\": \"pending_approval\", \"shippingAddress\": \"123 Main St, Anytown, USA\", \"billingAddress\": \"123 Main St, Anytown, USA\", \"createdBy\": \"65f000010000000000000001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/purchase-orders", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"]}}, "response": []}]}, {"name": "/api/inventory-transactions", "item": [{"name": "Get All Inventory Transactions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory-transactions", "host": ["{{baseUrl}}"], "path": ["api", "inventory-transactions"]}}, "response": []}, {"name": "Create Inventory Transaction", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"partId\": \"6640f0a0a1b2c3d4e5f6a00a\", \"warehouseId\": \"65f000000000000000000001\", \"transactionType\": \"stock_in_purchase\", \"quantity\": 10, \"previousStock\": 169, \"newStock\": 179, \"transactionDate\": \"2025-06-10T00:00:00.000Z\", \"referenceNumber\": \"PO-SAMPLE-002\", \"referenceType\": \"PurchaseOrder\", \"userId\": \"65f000010000000000000001\", \"notes\": \"Received stock from PO-SAMPLE-002\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/inventory-transactions", "host": ["{{baseUrl}}"], "path": ["api", "inventory-transactions"]}}, "response": []}]}, {"name": "/api/categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/categories", "host": ["{{baseUrl}}"], "path": ["api", "categories"]}}, "response": []}, {"name": "Create Category", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"name\": \"Sample Bearings Category\", \"description\": \"A sample category for bearings.\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/categories", "host": ["{{baseUrl}}"], "path": ["api", "categories"]}}, "response": []}]}, {"name": "/api/suppliers", "item": [{"name": "Get All Suppliers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"]}}, "response": []}, {"name": "Create Supplier", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"supplier_id\": \"SUP-SAMPLE-003\", \"name\": \"Sample Supplier Co.\", \"contactPerson\": \"<PERSON> Sample\", \"email\": \"<EMAIL>\", \"phone\": \"************\", \"address\": \"789 Sample Rd, Testburg\", \"is_active\": true}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/suppliers", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5174", "type": "string", "description": "The base URL for the API. Update this to your actual NEXT_PUBLIC_APP_URL or deployment URL."}]}